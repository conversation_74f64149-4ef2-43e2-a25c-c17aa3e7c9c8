package com.stadium.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stadium.common.api.ApiResult;
import com.stadium.dto.ComplaintDTO;
import com.stadium.entity.Complaint;
import com.stadium.service.ComplaintService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 投诉管理控制器
 * 处理用户对场馆的投诉
 */
@RestController
@RequestMapping("/api/complaints")
@RequiredArgsConstructor
public class ComplaintController {

    private final ComplaintService complaintService;

    /**
     * 分页查询投诉列表
     */
    @GetMapping
    @PreAuthorize("hasAuthority('complaint:list')")
    public ApiResult<Page<ComplaintDTO>> page(
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) Long venueId,
            @RequestParam(required = false) Integer status) {
        return ApiResult.success(complaintService.page(pageNum, pageSize, venueId, status));
    }

    /**
     * 获取投诉详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('complaint:get')")
    public ApiResult<ComplaintDTO> getById(@PathVariable Long id) {
        return ApiResult.success(complaintService.getById(id));
    }

    /**
     * 提交投诉
     */
    @PostMapping
    public ApiResult<Boolean> submit(@RequestBody @Valid Complaint complaint) {
        return ApiResult.success(complaintService.submit(complaint));
    }

    /**
     * 回复投诉
     */
    @PostMapping("/{id}/reply")
    @PreAuthorize("hasAuthority('complaint:reply')")
    public ApiResult<Boolean> reply(
            @PathVariable Long id,
            @RequestParam String replyContent) {
        return ApiResult.success(complaintService.reply(id, replyContent));
    }

    /**
     * 关闭投诉
     */
    @PostMapping("/{id}/close")
    @PreAuthorize("hasAuthority('complaint:close')")
    public ApiResult<Boolean> close(@PathVariable Long id) {
        return ApiResult.success(complaintService.close(id));
    }

    /**
     * 获取用户自己的投诉列表
     */
    @GetMapping("/my")
    public ApiResult<Page<ComplaintDTO>> myComplaints(
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) Integer status) {
        return ApiResult.success(complaintService.myComplaints(pageNum, pageSize, status));
    }
}