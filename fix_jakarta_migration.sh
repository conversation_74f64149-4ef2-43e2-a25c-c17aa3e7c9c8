#!/bin/bash

echo "开始执行 javax 到 jakarta 的迁移..."

# 替换 javax.servlet 为 jakarta.servlet
find backend/src/main/java -type f -name "*.java" -exec sed -i 's/javax\.servlet/jakarta.servlet/g' {} \;

# 替换 javax.validation 为 jakarta.validation
find backend/src/main/java -type f -name "*.java" -exec sed -i 's/javax\.validation/jakarta.validation/g' {} \;

# 替换 javax.annotation 为 jakarta.annotation
find backend/src/main/java -type f -name "*.java" -exec sed -i 's/javax\.annotation/jakarta.annotation/g' {} \;

# 替换 javax.persistence 为 jakarta.persistence (如果有的话)
find backend/src/main/java -type f -name "*.java" -exec sed -i 's/javax\.persistence/jakarta.persistence/g' {} \;

echo "javax 到 jakarta 迁移完成！"

# 显示修改的文件
echo "已修改的文件："
find backend/src/main/java -type f -name "*.java" -exec grep -l "jakarta\." {} \;
