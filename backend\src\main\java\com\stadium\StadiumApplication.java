package com.stadium;

import com.stadium.config.CorsProperties;
import com.stadium.config.JwtProperties;
import com.stadium.config.SpringDocProperties;
import com.stadium.config.StadiumSwaggerProperties;
import com.stadium.config.StadiumUploadProperties;
import com.stadium.config.properties.StadiumProperties;
import com.stadium.config.properties.TaskProperties;
import com.stadium.config.properties.CacheConfigProperties;
import com.stadium.config.properties.EncryptionProperties;
import com.stadium.config.properties.PerformanceProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

@SpringBootApplication
@EnableConfigurationProperties({
        JwtProperties.class,
        CorsProperties.class,
        PerformanceProperties.class,
        SpringDocProperties.class,
        StadiumSwaggerProperties.class,
        StadiumUploadProperties.class,
        StadiumProperties.class,
        TaskProperties.class,
        CacheConfigProperties.class,
        EncryptionProperties.class
})
@ComponentScan(basePackages = "com.stadium", excludeFilters = {
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.stadium\\.common\\.config\\..*")
})
public class StadiumApplication {
    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(StadiumApplication.class);

        // 添加自定义的ApplicationListener来处理启动完成事件
        app.addListeners(context -> {
            System.out.println("=== 体育场馆管理系统启动成功 ===");
            System.out.println("后端服务地址: http://localhost:8080/api");
            System.out.println("API文档地址: http://localhost:8080/api/doc.html");
        });

        try {
            app.run(args);
        } catch (Exception e) {
            // 如果是ddlApplicationRunner相关的错误，应用已经启动成功，忽略此错误
            if (e.getMessage() != null && e.getMessage().contains("ddlApplicationRunner")) {
                System.out.println("应用启动成功，忽略ddlApplicationRunner错误");
                // 保持JVM运行
                System.out.println("按 Ctrl+C 停止服务");
                try {
                    Thread.currentThread().join();
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                }
            } else {
                throw e;
            }
        }
    }
}