package com.stadium.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 用户更新请求
 */
public class UserUpdateRequest {

    @Data
    @Schema(description = "修改密码请求")
    public static class Password {
        @NotBlank(message = "旧密码不能为空")
        @Schema(description = "旧密码")
        private String oldPassword;

        @NotBlank(message = "新密码不能为空")
        @Pattern(regexp = "^[a-zA-Z0-9_]{6,20}$", message = "密码长度必须在6-20位之间，只能包含字母、数字和下划线")
        @Schema(description = "新密码")
        private String newPassword;
    }

    @Data
    @Schema(description = "重置密码请求")
    public static class PasswordReset {
        @NotBlank(message = "密码不能为空")
        @Pattern(regexp = "^[a-zA-Z0-9_]{6,20}$", message = "密码长度必须在6-20位之间，只能包含字母、数字和下划线")
        @Schema(description = "新密码")
        private String password;
    }

    @Data
    @Schema(description = "更新状态请求")
    public static class Status {
        @NotNull(message = "状态不能为空")
        @Schema(description = "状态：0-禁用，1-启用")
        private Integer status;
    }

    @Data
    @Schema(description = "更新角色请求")
    public static class Role {
        @NotBlank(message = "角色不能为空")
        @Pattern(regexp = "^(ADMIN|USER)$", message = "角色只能是ADMIN或USER")
        @Schema(description = "角色：ADMIN-管理员，USER-普通用户")
        private String role;
    }
}