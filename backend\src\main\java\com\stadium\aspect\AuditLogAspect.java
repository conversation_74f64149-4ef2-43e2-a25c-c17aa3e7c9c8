package com.stadium.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stadium.entity.AuditLog;
import com.stadium.mapper.AuditLogMapper;
import com.stadium.util.SecurityUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Arrays;

@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class AuditLogAspect {

    private final AuditLogMapper auditLogMapper;
    private final ObjectMapper objectMapper;

    @Around("@annotation(com.stadium.annotation.AuditLog)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = null;
        Exception exception = null;

        try {
            result = joinPoint.proceed();
            return result;
        } catch (Exception e) {
            exception = e;
            throw e;
        } finally {
            try {
                saveAuditLog(joinPoint, startTime, result, exception);
            } catch (Exception e) {
                log.error("保存审计日志失败", e);
            }
        }
    }

    private void saveAuditLog(ProceedingJoinPoint joinPoint, long startTime, Object result, Exception exception) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            String methodName = signature.getMethod().getName();
            String className = joinPoint.getTarget().getClass().getSimpleName();
            String operation = className + "." + methodName;

            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder
                    .getRequestAttributes();
            HttpServletRequest request = attributes != null ? attributes.getRequest() : null;
            String ip = SecurityUtil.getCurrentIp();
            Long userId = SecurityUtil.getCurrentUserId();

            AuditLog auditLog = new AuditLog();
            auditLog.setUserId(userId);
            auditLog.setOperation(operation);
            if (request != null) {
                auditLog.setMethod(request.getMethod());
                auditLog.setUrl(request.getRequestURI());
            }
            auditLog.setIp(ip);
            auditLog.setParams(objectMapper.writeValueAsString(Arrays.asList(joinPoint.getArgs())));
            auditLog.setResult(result != null ? objectMapper.writeValueAsString(result) : null);
            auditLog.setError(exception != null ? exception.getMessage() : null);
            auditLog.setDuration(System.currentTimeMillis() - startTime);
            auditLog.setCreateTime(LocalDateTime.now());

            auditLogMapper.insert(auditLog);
        } catch (Exception e) {
            log.error("构建审计日志失败", e);
        }
    }
}