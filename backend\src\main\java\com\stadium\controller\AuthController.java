package com.stadium.controller;

import com.stadium.common.api.ApiResult;
import com.stadium.dto.LoginDTO;
import com.stadium.dto.RegisterDTO;
import com.stadium.dto.UserDTO;
import com.stadium.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 */
@Tag(name = "认证管理")
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
public class AuthController {

    private final AuthService authService;

    @Operation(summary = "用户登录")
    @PostMapping("/login")
    public ApiResult<Object> login(@RequestBody @Valid LoginDTO loginDTO) {
        try {
            System.out.println("=== AuthController: 接收到登录请求 ===");
            System.out.println("登录请求参数: username=" + loginDTO.getUsername() + ", password=******" +
                    (loginDTO.getRole() != null ? ", role=" + loginDTO.getRole() : ""));

            String token = authService.login(loginDTO);

            // 获取当前用户信息
            UserDTO userInfo = authService.getCurrentUser();

            // 创建包含token和用户信息的响应
            Map<String, Object> response = new HashMap<>();
            response.put("token", token);
            response.put("userInfo", userInfo);

            // 如果提供了角色，添加到响应中
            if (loginDTO.getRole() != null && !loginDTO.getRole().isEmpty()) {
                response.put("role", loginDTO.getRole());
            }

            System.out.println("登录成功，返回token和用户信息");
            System.out.println("=== AuthController: 登录请求处理完成 ===");

            return ApiResult.success(response);
        } catch (Exception e) {
            System.err.println("=== AuthController: 登录失败 ===");
            System.err.println("登录失败原因: " + e.getMessage());
            e.printStackTrace();
            return ApiResult.failed(e.getMessage());
        }
    }

    @Operation(summary = "用户注册")
    @PostMapping("/register")
    public ApiResult<Void> register(@RequestBody @Valid RegisterDTO registerDTO) {
        authService.register(registerDTO);
        return ApiResult.success();
    }

    @Operation(summary = "用户登出")
    @PostMapping("/logout")
    public ApiResult<Void> logout() {
        authService.logout();
        return ApiResult.success();
    }

    @Operation(summary = "刷新令牌")
    @PostMapping("/refresh-token")
    public ApiResult<String> refreshToken() {
        return ApiResult.success(authService.refreshToken());
    }

    @Operation(summary = "重置密码")
    @PostMapping("/reset-password")
    public ApiResult<Void> resetPassword(
            @RequestParam String oldPassword,
            @RequestParam String newPassword) {
        authService.resetPassword(oldPassword, newPassword);
        return ApiResult.success();
    }

    @Operation(summary = "获取当前登录用户信息")
    @GetMapping("/current-user")
    public ApiResult<UserDTO> getCurrentUser() {
        return ApiResult.success(authService.getCurrentUser());
    }

    @Operation(summary = "忘记密码")
    @PostMapping("/forgot-password")
    public ApiResult<Void> forgotPassword(
            @RequestParam String email,
            @RequestParam String verificationCode,
            @RequestParam String newPassword) {
        authService.forgotPassword(email, verificationCode, newPassword);
        return ApiResult.success();
    }

    @Operation(summary = "验证邮箱")
    @PostMapping("/verify-email")
    public ApiResult<Void> verifyEmail(
            @RequestParam String email,
            @RequestParam String verificationCode) {
        authService.verifyEmail(email, verificationCode);
        return ApiResult.success();
    }
}