package com.stadium.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stadium.common.api.ApiResult;
import com.stadium.dto.VenueTypeDTO;
import com.stadium.entity.VenueType;
import com.stadium.service.VenueTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 场馆类型管理控制器
 */
@Tag(name = "场馆类型管理")
@RestController
@RequestMapping("/api/venue-types")
@RequiredArgsConstructor
public class VenueTypeController {

    private final VenueTypeService venueTypeService;

    @Operation(summary = "创建场馆类型")
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResult<VenueType> createVenueType(@RequestBody @Valid VenueTypeDTO venueTypeDTO) {
        return ApiResult.success(venueTypeService.createVenueType(venueTypeDTO));
    }

    @Operation(summary = "更新场馆类型")
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResult<VenueType> updateVenueType(@PathVariable Long id, @RequestBody @Valid VenueTypeDTO venueTypeDTO) {
        return ApiResult.success(venueTypeService.updateVenueType(id, venueTypeDTO));
    }

    @Operation(summary = "删除场馆类型")
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResult<Void> deleteVenueType(@PathVariable Long id) {
        venueTypeService.deleteVenueType(id);
        return ApiResult.success();
    }

    @Operation(summary = "获取场馆类型详情")
    @GetMapping("/{id}")
    public ApiResult<VenueType> getVenueType(@PathVariable Long id) {
        return ApiResult.success(venueTypeService.getVenueType(id));
    }

    @Operation(summary = "分页查询场馆类型列表")
    @GetMapping
    public ApiResult<Page<VenueType>> listVenueTypes(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword) {
        return ApiResult.success(venueTypeService.listVenueTypes(current, size, keyword));
    }
}