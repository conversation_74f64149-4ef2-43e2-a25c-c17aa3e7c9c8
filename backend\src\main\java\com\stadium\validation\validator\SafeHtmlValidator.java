package com.stadium.validation.validator;

import com.stadium.validation.annotation.SafeHtml;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.regex.Pattern;

/**
 * 安全HTML验证器
 */
public class SafeHtmlValidator implements ConstraintValidator<SafeHtml, String> {

    private String[] allowedTags;
    private String[] allowedAttributes;

    // 危险标签和属性的正则表达式
    private Pattern dangerousTagsPattern;
    private Pattern dangerousAttributesPattern;

    @Override
    public void initialize(SafeHtml constraintAnnotation) {
        this.allowedTags = constraintAnnotation.allowedTags();
        this.allowedAttributes = constraintAnnotation.allowedAttributes();

        // 构建危险标签的正则表达式
        String dangerousTags = Arrays
                .stream(new String[] { "script", "iframe", "object", "embed", "form", "style", "link", "meta", "base" })
                .filter(tag -> !Arrays.asList(allowedTags).contains(tag))
                .reduce((a, b) -> a + "|" + b)
                .orElse("");

        if (!dangerousTags.isEmpty()) {
            dangerousTagsPattern = Pattern.compile(
                    "<(" + dangerousTags + ")[^>]*>.*?<\\/\\1>|<(" + dangerousTags + ")[^>]*\\/?>",
                    Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
        }

        // 构建危险属性的正则表达式
        String dangerousAttributes = Arrays
                .stream(new String[] { "on\\w+", "formaction", "action", "javascript:", "vbscript:", "data:" })
                .filter(attr -> !Arrays.asList(allowedAttributes).contains(attr))
                .reduce((a, b) -> a + "|" + b)
                .orElse("");

        if (!dangerousAttributes.isEmpty()) {
            dangerousAttributesPattern = Pattern.compile("(" + dangerousAttributes + ")\\s*=\\s*([\"'])?[^\\s>\"']*\\2",
                    Pattern.CASE_INSENSITIVE);
        }
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (StringUtils.isBlank(value)) {
            return true;
        }

        // 检查是否包含危险标签
        if (dangerousTagsPattern != null && dangerousTagsPattern.matcher(value).find()) {
            return false;
        }

        // 检查是否包含危险属性
        if (dangerousAttributesPattern != null && dangerousAttributesPattern.matcher(value).find()) {
            return false;
        }

        // 检查是否包含JavaScript URL
        if (value.toLowerCase().contains("javascript:")) {
            return false;
        }

        // 检查是否包含VBScript URL
        if (value.toLowerCase().contains("vbscript:")) {
            return false;
        }

        // 检查是否包含Data URL
        if (value.toLowerCase().contains("data:")) {
            return false;
        }

        return true;
    }
}
