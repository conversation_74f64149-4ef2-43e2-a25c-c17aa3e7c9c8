package com.stadium.config;

import com.stadium.exception.BusinessException;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.io.File;

/**
 * 导出配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "export")
public class ExportConfig {
    private String baseDir;

    @PostConstruct
    public void init() {
        try {
            if (baseDir == null || baseDir.trim().isEmpty()) {
                baseDir = "./exports";
            }

            File dir = new File(baseDir);
            if (!dir.exists()) {
                if (!dir.mkdirs()) {
                    throw new BusinessException("创建导出目录失败: " + baseDir);
                }
            }
            if (!dir.canWrite()) {
                throw new BusinessException("导出目录没有写入权限: " + baseDir);
            }
        } catch (Exception e) {
            throw new BusinessException("初始化导出目录失败: " + e.getMessage());
        }
    }

    public String getExportPath() {
        return baseDir;
    }
}