package com.stadium.controller;

import com.stadium.common.api.ApiResult;
import com.stadium.util.CaptchaUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMethod;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.util.UUID;

/**
 * 验证码控制器
 */
@Slf4j
@Tag(name = "验证码接口")
@RestController
@RequestMapping("/api/captcha")
@CrossOrigin(origins = "*", allowedHeaders = "*", methods = { RequestMethod.GET, RequestMethod.POST,
        RequestMethod.OPTIONS })
public class CaptchaController {

    @Autowired
    private CaptchaUtil captchaUtil;

    @Operation(summary = "处理OPTIONS预检请求")
    @RequestMapping(value = "/generate", method = RequestMethod.OPTIONS)
    public void handleOptions(HttpServletResponse response) {
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers",
                "Origin, X-Requested-With, Content-Type, Accept, Authorization");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setStatus(HttpServletResponse.SC_OK);
    }

    @Operation(summary = "生成验证码")
    @GetMapping("/generate")
    public void generateCaptcha(HttpServletRequest request, HttpServletResponse response) {
        // 添加CORS头
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers",
                "Origin, X-Requested-With, Content-Type, Accept, Authorization");
        response.setHeader("Access-Control-Max-Age", "3600");
        // 生成验证码ID
        String captchaId = UUID.randomUUID().toString();
        log.info("Generating captcha with ID: {}", captchaId);

        try {
            // 生成验证码图片
            BufferedImage image = captchaUtil.generateCaptchaImage(captchaId);
            if (image == null) {
                log.error("Failed to generate captcha image for ID: {}", captchaId);
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                return;
            }
            log.info("Generated captcha image for ID: {}, size: {}x{}", captchaId, image.getWidth(), image.getHeight());

            // 设置响应头
            response.setHeader("Captcha-Id", captchaId);
            response.setContentType("image/jpeg");
            response.setDateHeader("Expires", 0);
            response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate");
            response.setHeader("Pragma", "no-cache");

            // CORS 相关头 - 允许所有来源访问
            String origin = "*";
            // 如果请求头中有Origin，则使用该值
            String requestOrigin = request.getHeader("Origin");
            if (requestOrigin != null && !requestOrigin.isEmpty()) {
                origin = requestOrigin;
                log.info("Setting CORS origin to request origin: {}", origin);
            }

            response.setHeader("Access-Control-Allow-Origin", origin);
            response.setHeader("Access-Control-Allow-Credentials", "true");
            response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
            response.setHeader("Access-Control-Allow-Headers",
                    "Origin, X-Requested-With, Content-Type, Accept, Authorization");
            response.setHeader("Access-Control-Expose-Headers", "Captcha-Id");

            log.info("Setting CORS headers for captcha response");

            // 输出图片
            try (ServletOutputStream out = response.getOutputStream()) {
                boolean success = ImageIO.write(image, "jpg", out);
                if (!success) {
                    log.error("Failed to write image to output stream for ID: {}", captchaId);
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    return;
                }
                out.flush();
                log.info("Successfully sent captcha image for ID: {}", captchaId);
            }
        } catch (Exception e) {
            log.error("Error generating captcha: {}", e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "验证验证码")
    @PostMapping("/verify")
    public ApiResult<Boolean> verifyCaptcha(@RequestParam String captchaKey, @RequestParam String captcha) {
        log.info("Verifying captcha - ID: {}, Code: {}", captchaKey, captcha);
        boolean isValid = captchaUtil.validateCaptcha(captchaKey, captcha);
        return ApiResult.success(isValid);
    }
}