package com.stadium.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stadium.common.api.ApiResult;
import com.stadium.entity.ActivityCheckIn;
import com.stadium.entity.ActivityCheckInStatistics;
import com.stadium.service.ActivityCheckInService;
import com.stadium.service.ActivityCheckInStatisticsService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.time.LocalDateTime;

/**
 * 活动签到控制器
 */
@Tag(name = "活动签到管理")
@RestController
@RequestMapping("/api/activity/check-in")
@RequiredArgsConstructor
public class ActivityCheckInController {

    private final ActivityCheckInService checkInService;
    private final ActivityCheckInStatisticsService activityCheckInStatisticsService;

    @Operation(summary = "现场签到")
    @PostMapping("/{registrationId}")
    public ApiResult<Boolean> checkIn(
            @Parameter(description = "报名记录ID") @PathVariable Long registrationId,
            @Parameter(description = "操作人ID") @RequestParam Long operatorId,
            @Parameter(description = "备注") @RequestParam(required = false) String remark) {
        return ApiResult.success(checkInService.checkIn(registrationId, operatorId, remark));
    }

    @Operation(summary = "扫码签到")
    @PostMapping("/qr-code/{registrationId}")
    public ApiResult<Boolean> checkInByQrCode(
            @Parameter(description = "报名记录ID") @PathVariable Long registrationId,
            @Parameter(description = "二维码内容") @RequestParam String qrCode) {
        return ApiResult.success(checkInService.checkInByQrCode(registrationId, qrCode));
    }

    @Operation(summary = "获取活动签到记录")
    @GetMapping("/activity/{activityId}")
    public ApiResult<Page<ActivityCheckIn>> getActivityCheckIns(
            @Parameter(description = "活动ID") @PathVariable Long activityId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
        return ApiResult.success(checkInService.getActivityCheckIns(activityId, new Page<>(current, size)));
    }

    @Operation(summary = "获取用户签到记录")
    @GetMapping("/user/{userId}")
    public ApiResult<Page<ActivityCheckIn>> getUserCheckIns(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
        return ApiResult.success(checkInService.getUserCheckIns(userId, new Page<>(current, size)));
    }

    @Operation(summary = "获取签到统计信息")
    @GetMapping("/statistics/{activityId}")
    public ApiResult<ActivityCheckInStatistics> getCheckInStatistics(
            @Parameter(description = "活动ID") @PathVariable Long activityId) {
        return ApiResult.success(checkInService.getCheckInStatistics(activityId));
    }

    @Operation(summary = "生成签到二维码")
    @GetMapping("/qr-code/{activityId}")
    public ApiResult<String> generateCheckInQrCode(
            @Parameter(description = "活动ID") @PathVariable Long activityId) {
        return ApiResult.success(checkInService.generateCheckInQrCode(activityId));
    }

    @Operation(summary = "导出活动签到记录")
    @GetMapping("/export/activity/{activityId}")
    public void exportActivityCheckIns(
            @Parameter(description = "活动ID") @PathVariable Long activityId,
            HttpServletResponse response) throws IOException {
        String filePath = checkInService.exportActivityCheckIns(activityId);
        File file = new File(filePath);

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + file.getName());

        try (FileInputStream fis = new FileInputStream(file);
                OutputStream os = response.getOutputStream()) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = fis.read(buffer)) > 0) {
                os.write(buffer, 0, len);
            }
        }

        // 删除临时文件
        file.delete();
    }

    @Operation(summary = "导出用户签到记录")
    @GetMapping("/export/user/{userId}")
    public void exportUserCheckIns(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            HttpServletResponse response) throws IOException {
        String filePath = checkInService.exportUserCheckIns(userId);
        File file = new File(filePath);

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + file.getName());

        try (FileInputStream fis = new FileInputStream(file);
                OutputStream os = response.getOutputStream()) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = fis.read(buffer)) > 0) {
                os.write(buffer, 0, len);
            }
        }

        // 删除临时文件
        file.delete();
    }

    @Operation(summary = "导出签到统计数据")
    @GetMapping("/{activityId}/check-in/statistics/export")
    public void exportCheckInStatistics(
            @Parameter(description = "活动ID") @PathVariable Long activityId,
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            HttpServletResponse response) throws IOException {
        String filePath = activityCheckInStatisticsService.exportCheckInStatistics(activityId, startTime, endTime);
        File file = new File(filePath);

        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment; filename=" + file.getName());

        try (FileInputStream fis = new FileInputStream(file);
                OutputStream os = response.getOutputStream()) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = fis.read(buffer)) > 0) {
                os.write(buffer, 0, len);
            }
        } finally {
            Files.deleteIfExists(file.toPath());
        }
    }
}