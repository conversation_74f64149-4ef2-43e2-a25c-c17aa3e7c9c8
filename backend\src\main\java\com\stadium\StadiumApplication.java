package com.stadium;

import com.stadium.config.CorsProperties;
import com.stadium.config.JwtProperties;
import com.stadium.config.SpringDocProperties;
import com.stadium.config.StadiumSwaggerProperties;
import com.stadium.config.StadiumUploadProperties;
import com.stadium.config.properties.StadiumProperties;
import com.stadium.config.properties.TaskProperties;
import com.stadium.config.properties.CacheConfigProperties;
import com.stadium.config.properties.EncryptionProperties;
import com.stadium.config.properties.PerformanceProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

@SpringBootApplication
@EnableConfigurationProperties({
        JwtProperties.class,
        CorsProperties.class,
        PerformanceProperties.class,
        SpringDocProperties.class,
        StadiumSwaggerProperties.class,
        StadiumUploadProperties.class,
        StadiumProperties.class,
        TaskProperties.class,
        CacheConfigProperties.class,
        EncryptionProperties.class
})
@ComponentScan(basePackages = "com.stadium", excludeFilters = {
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.stadium\\.common\\.config\\..*")
})
public class StadiumApplication {
    public static void main(String[] args) {
        SpringApplication.run(StadiumApplication.class, args);
    }
}