package com.stadium.controller;

import com.stadium.common.api.ApiResult;
import com.stadium.entity.UserWallet;
import com.stadium.entity.WalletTransaction;
import com.stadium.service.UserWalletService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 钱包管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/wallet")
@Tag(name = "钱包管理接口")
@RequiredArgsConstructor
public class WalletController {

    private final UserWalletService userWalletService;

    /**
     * 获取用户钱包信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取钱包信息")
    public ApiResult<UserWallet> getWalletInfo(@RequestParam Long userId) {
        UserWallet wallet = userWalletService.getByUserId(userId);
        return ApiResult.success(wallet);
    }

    /**
     * 充值
     */
    @PostMapping("/recharge")
    @Operation(summary = "账户充值")
    @PreAuthorize("hasAuthority('wallet:recharge')")
    public ApiResult<Boolean> recharge(@RequestParam Long userId,
            @RequestParam BigDecimal amount,
            @RequestParam(required = false) String description,
            @RequestParam(required = false) Long operatorId) {
        String desc = description != null ? description : "账户充值";
        boolean result = userWalletService.recharge(userId, amount, desc, operatorId);
        return ApiResult.success(result);
    }

    /**
     * 消费
     */
    @PostMapping("/consume")
    @Operation(summary = "账户消费")
    public ApiResult<Boolean> consume(@RequestParam Long userId,
            @RequestParam BigDecimal amount,
            @RequestParam String description,
            @RequestParam(required = false) String orderNo) {
        boolean result = userWalletService.consume(userId, amount, description, orderNo);
        return ApiResult.success(result);
    }

    /**
     * 退款
     */
    @PostMapping("/refund")
    @Operation(summary = "账户退款")
    @PreAuthorize("hasAuthority('wallet:refund')")
    public ApiResult<Boolean> refund(@RequestParam Long userId,
            @RequestParam BigDecimal amount,
            @RequestParam String description,
            @RequestParam(required = false) String orderNo) {
        boolean result = userWalletService.refund(userId, amount, description, orderNo);
        return ApiResult.success(result);
    }

    /**
     * 检查余额
     */
    @GetMapping("/check-balance")
    @Operation(summary = "检查余额是否足够")
    public ApiResult<Boolean> checkBalance(@RequestParam Long userId,
            @RequestParam BigDecimal amount) {
        boolean sufficient = userWalletService.checkBalance(userId, amount);
        return ApiResult.success(sufficient);
    }

    /**
     * 设置支付密码
     */
    @PostMapping("/set-pay-password")
    @Operation(summary = "设置支付密码")
    public ApiResult<Boolean> setPayPassword(@RequestParam Long userId,
            @RequestParam String payPassword) {
        boolean result = userWalletService.setPayPassword(userId, payPassword);
        return ApiResult.success(result);
    }

    /**
     * 验证支付密码
     */
    @PostMapping("/verify-pay-password")
    @Operation(summary = "验证支付密码")
    public ApiResult<Boolean> verifyPayPassword(@RequestParam Long userId,
            @RequestParam String payPassword) {
        boolean result = userWalletService.verifyPayPassword(userId, payPassword);
        return ApiResult.success(result);
    }

    /**
     * 获取交易记录
     */
    @GetMapping("/transactions")
    @Operation(summary = "获取交易记录")
    public ApiResult<List<WalletTransaction>> getTransactions(@RequestParam Long userId,
            @RequestParam(required = false) Integer type,
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        List<WalletTransaction> transactions = userWalletService.getTransactions(userId, type, pageNum, pageSize);
        return ApiResult.success(transactions);
    }

    /**
     * 获取交易统计
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取交易统计")
    public ApiResult<Map<String, Object>> getStatistics(@RequestParam Long userId) {
        Map<String, Object> statistics = userWalletService.getTransactionStatistics(userId);
        return ApiResult.success(statistics);
    }

    /**
     * 获取时间范围内的交易记录
     */
    @GetMapping("/transactions/range")
    @Operation(summary = "获取时间范围内的交易记录")
    public ApiResult<List<WalletTransaction>> getTransactionsByTimeRange(@RequestParam Long userId,
            @RequestParam LocalDateTime startTime,
            @RequestParam LocalDateTime endTime) {
        List<WalletTransaction> transactions = userWalletService.getTransactionsByTimeRange(userId, startTime, endTime);
        return ApiResult.success(transactions);
    }

    /**
     * 获取每日交易统计（管理员）
     */
    @GetMapping("/daily-statistics")
    @Operation(summary = "获取每日交易统计")
    @PreAuthorize("hasAuthority('wallet:statistics')")
    public ApiResult<List<Map<String, Object>>> getDailyStatistics(@RequestParam LocalDateTime startTime,
            @RequestParam LocalDateTime endTime) {
        List<Map<String, Object>> statistics = userWalletService.getDailyStatistics(startTime, endTime);
        return ApiResult.success(statistics);
    }

    /**
     * 批量创建钱包（系统初始化）
     */
    @PostMapping("/init-wallets")
    @Operation(summary = "批量创建钱包")
    @PreAuthorize("hasAuthority('system:init')")
    public ApiResult<String> initWallets() {
        userWalletService.createWalletsForExistingUsers();
        return ApiResult.success("钱包初始化完成");
    }

    /**
     * 冻结金额
     */
    @PostMapping("/freeze")
    @Operation(summary = "冻结金额")
    @PreAuthorize("hasAuthority('wallet:freeze')")
    public ApiResult<Boolean> freezeAmount(@RequestParam Long userId,
            @RequestParam BigDecimal amount,
            @RequestParam String description) {
        boolean result = userWalletService.freezeAmount(userId, amount, description);
        return ApiResult.success(result);
    }

    /**
     * 解冻金额
     */
    @PostMapping("/unfreeze")
    @Operation(summary = "解冻金额")
    @PreAuthorize("hasAuthority('wallet:unfreeze')")
    public ApiResult<Boolean> unfreezeAmount(@RequestParam Long userId,
            @RequestParam BigDecimal amount,
            @RequestParam String description) {
        boolean result = userWalletService.unfreezeAmount(userId, amount, description);
        return ApiResult.success(result);
    }

    /**
     * 扣除冻结金额
     */
    @PostMapping("/deduct-frozen")
    @Operation(summary = "扣除冻结金额")
    @PreAuthorize("hasAuthority('wallet:deduct')")
    public ApiResult<Boolean> deductFrozenAmount(@RequestParam Long userId,
            @RequestParam BigDecimal amount,
            @RequestParam String description) {
        boolean result = userWalletService.deductFrozenAmount(userId, amount, description);
        return ApiResult.success(result);
    }
}
