package com.stadium.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 任务执行配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "task")
public class TaskProperties {

    /**
     * 执行配置
     */
    private Execution execution = new Execution();

    @Data
    public static class Execution {
        /**
         * 线程池配置
         */
        private Pool pool = new Pool();

        @Data
        public static class Pool {
            /**
             * 核心线程数
             */
            private Integer coreSize = 8;

            /**
             * 最大线程数
             */
            private Integer maxSize = 16;

            /**
             * 队列容量
             */
            private Integer queueCapacity = 100;

            /**
             * 线程名前缀
             */
            private String threadNamePrefix = "stadium-task-";

            /**
             * 是否允许核心线程超时
             */
            private Boolean allowCoreThreadTimeout = true;

            /**
             * 线程保活时间（秒）
             */
            private Integer keepAlive = 60;
        }
    }
}
