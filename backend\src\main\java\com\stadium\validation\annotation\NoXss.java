package com.stadium.validation.annotation;

import com.stadium.validation.validator.NoXssValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * 禁止XSS攻击注解
 */
@Documented
@Constraint(validatedBy = NoXssValidator.class)
@Target({ ElementType.FIELD, ElementType.PARAMETER })
@Retention(RetentionPolicy.RUNTIME)
public @interface NoXss {

    String message() default "包含XSS攻击特征";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
