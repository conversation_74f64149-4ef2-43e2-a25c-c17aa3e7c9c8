package com.stadium.security;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.stadium.common.api.ApiResult;
import com.stadium.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.util.StreamUtils;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 登录认证过滤器，处理JSON格式的登录请求
 */
@Slf4j
public class LoginAuthenticationFilter extends UsernamePasswordAuthenticationFilter {

    private final JwtUtil jwtUtil;
    private boolean postOnly = true;

    public LoginAuthenticationFilter(AuthenticationManager authenticationManager, JwtUtil jwtUtil) {
        super(authenticationManager);
        this.jwtUtil = jwtUtil;
        // 设置登录处理URL
        this.setFilterProcessesUrl("/api/auth/login");
        log.info("初始化 LoginAuthenticationFilter，登录URL: /api/auth/login");
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response)
            throws AuthenticationException {
        if (postOnly && !request.getMethod().equals("POST")) {
            throw new AuthenticationServiceException("Authentication method not supported: " + request.getMethod());
        }

        try {
            // 从请求体中读取JSON数据
            String body = StreamUtils.copyToString(request.getInputStream(), StandardCharsets.UTF_8);
            log.info("接收到登录请求: {}", body);

            JSONObject jsonObject = JSON.parseObject(body);
            String username = jsonObject.getString("username");
            String password = jsonObject.getString("password");

            if (username == null) {
                username = "";
            }
            if (password == null) {
                password = "";
            }

            username = username.trim();
            UsernamePasswordAuthenticationToken authRequest = new UsernamePasswordAuthenticationToken(username,
                    password);

            // 设置请求详情
            setDetails(request, authRequest);
            log.info("尝试认证用户: {}", username);

            // 进行认证
            return this.getAuthenticationManager().authenticate(authRequest);
        } catch (IOException e) {
            log.error("处理登录请求时发生错误: {}", e.getMessage(), e);
            throw new AuthenticationServiceException("无法解析登录请求", e);
        }
    }

    @Override
    protected void successfulAuthentication(HttpServletRequest request, HttpServletResponse response,
            FilterChain chain, Authentication authResult) throws IOException, ServletException {
        UserDetails userDetails = (UserDetails) authResult.getPrincipal();
        log.info("用户 {} 认证成功", userDetails.getUsername());

        // 生成JWT令牌
        String token = jwtUtil.generateToken(userDetails);
        log.info("为用户 {} 生成JWT令牌: {}", userDetails.getUsername(), token);

        // 准备返回数据
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);

        // 添加用户信息
        if (userDetails instanceof CustomUserDetails) {
            CustomUserDetails customUserDetails = (CustomUserDetails) userDetails;
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", customUserDetails.getUserId());
            userInfo.put("username", customUserDetails.getUsername());
            userInfo.put("realName", customUserDetails.getUser().getRealName());
            userInfo.put("role", customUserDetails.getUser().getRole());
            result.put("userInfo", userInfo);
        }

        // 返回成功响应
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write(JSON.toJSONString(ApiResult.success(result)));
    }

    @Override
    protected void unsuccessfulAuthentication(HttpServletRequest request, HttpServletResponse response,
            AuthenticationException failed) throws IOException, ServletException {
        log.info("认证失败: {}", failed.getMessage());

        // 返回失败响应
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.getWriter().write(JSON.toJSONString(ApiResult.failed(401, "认证失败: " + failed.getMessage())));
    }
}