@echo off
chcp 65001
echo ========================================
echo 体育场馆管理系统兼容性验证
echo ========================================
echo.

echo 正在验证Java版本...
java -version 2>&1 | findstr "version"
if %errorlevel% neq 0 (
    echo [错误] Java环境未正确配置
    pause
    exit /b 1
)

echo.
echo 正在验证Maven环境...
mvn -version 2>&1 | findstr "Apache Maven"
if %errorlevel% neq 0 (
    echo [错误] Maven环境未正确配置
    pause
    exit /b 1
)

echo.
echo 正在检查项目结构...
if not exist "backend\pom.xml" (
    echo [错误] 后端项目结构不完整
    pause
    exit /b 1
)

if not exist "frontend\package.json" (
    echo [错误] 前端项目结构不完整
    pause
    exit /b 1
)

echo.
echo 正在验证依赖配置...
cd backend
echo 检查Maven依赖解析...
mvn dependency:resolve -q
if %errorlevel% neq 0 (
    echo [警告] Maven依赖解析可能存在问题
) else (
    echo [成功] Maven依赖解析正常
)

echo.
echo 正在验证编译兼容性...
mvn compile -q -DskipTests
if %errorlevel% neq 0 (
    echo [警告] 编译可能存在问题，请检查日志
) else (
    echo [成功] 编译验证通过
)

cd ..

echo.
echo ========================================
echo 兼容性验证完成
echo ========================================
echo.
echo 如果所有检查都通过，系统应该可以正常启动
echo 使用 start-all.bat 启动完整系统
echo.
pause
