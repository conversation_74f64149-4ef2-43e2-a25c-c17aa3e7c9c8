package com.stadium.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
@Schema(description = "场馆类型信息")
public class VenueTypeDTO {

    @NotBlank(message = "类型名称不能为空")
    @Size(min = 2, max = 50, message = "类型名称长度必须在2-50个字符之间")
    @Schema(description = "类型名称", required = true)
    private String name;

    @Size(max = 200, message = "描述长度不能超过200个字符")
    @Schema(description = "类型描述")
    private String description;

    @Schema(description = "排序号")
    private Integer sort;
}