package com.stadium;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 编译测试类
 * 验证所有类能够正确编译和加载
 */
@SpringBootTest
@ActiveProfiles("test")
public class CompilationTest {

    @Test
    public void testCompilation() {
        // 如果这个测试能够运行，说明编译没有问题
        System.out.println("编译测试通过！");
    }

    @Test
    public void testJakartaImports() {
        try {
            // 测试Jakarta EE包是否正确导入
            Class.forName("jakarta.servlet.http.HttpServletRequest");
            Class.forName("jakarta.validation.constraints.NotNull");
            Class.forName("jakarta.annotation.PostConstruct");
            
            System.out.println("Jakarta EE 包导入测试通过！");
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("Jakarta EE 包导入失败", e);
        }
    }

    @Test
    public void testSpringBootClasses() {
        try {
            // 测试Spring Boot 3.x核心类
            Class.forName("org.springframework.boot.SpringApplication");
            Class.forName("org.springframework.security.config.annotation.web.builders.HttpSecurity");
            Class.forName("com.baomidou.mybatisplus.core.mapper.BaseMapper");
            
            System.out.println("Spring Boot 3.x 核心类测试通过！");
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("Spring Boot 3.x 核心类加载失败", e);
        }
    }

    @Test
    public void testProjectClasses() {
        try {
            // 测试项目核心类
            Class.forName("com.stadium.StadiumApplication");
            Class.forName("com.stadium.config.SecurityConfig");
            Class.forName("com.stadium.config.SwaggerConfig");
            Class.forName("com.stadium.util.JwtUtil");
            
            System.out.println("项目核心类测试通过！");
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("项目核心类加载失败", e);
        }
    }
}
