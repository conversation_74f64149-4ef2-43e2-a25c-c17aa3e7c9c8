package com.stadium.dto.auth;

import com.stadium.validation.annotation.NoSqlInjection;
import com.stadium.validation.annotation.NoXss;
import com.stadium.validation.annotation.StrongPassword;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

/**
 * 注册请求DTO
 */
@Data
@Schema(description = "用户注册信息")
public class RegisterDTO {
    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9_]{4,16}$", message = "用户名必须是4-16位字母、数字或下划线")
    @NoXss(message = "用户名包含不安全的字符")
    @NoSqlInjection(message = "用户名包含不安全的字符")
    @Schema(description = "用户名", required = true)
    private String username;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @StrongPassword(minLength = 8, requireUppercase = true, requireLowercase = true, requireDigit = true, requireSpecialChar = false, message = "密码必须包含大小写字母和数字，且长度不少于8位")
    @Schema(description = "密码", required = true)
    private String password;

    /**
     * 确认密码
     */
    @NotBlank(message = "确认密码不能为空")
    @Schema(description = "确认密码", required = true)
    private String confirmPassword;

    /**
     * 昵称
     */
    @Size(min = 2, max = 20, message = "昵称长度必须在2-20位之间")
    @NoXss(message = "昵称包含不安全的字符")
    @NoSqlInjection(message = "昵称包含不安全的字符")
    @Schema(description = "昵称")
    private String nickname;

    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @NoXss(message = "邮箱包含不安全的字符")
    @NoSqlInjection(message = "邮箱包含不安全的字符")
    @Schema(description = "邮箱", required = true)
    private String email;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Schema(description = "手机号", required = true)
    private String phone;

    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", message = "身份证号格式不正确")
    @Schema(description = "身份证号")
    private String idCard;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空")
    @Schema(description = "验证码", required = true)
    private String captcha;

    /**
     * 验证码key
     */
    @NotBlank(message = "验证码key不能为空")
    @Schema(description = "验证码key", required = true)
    private String captchaKey;
}