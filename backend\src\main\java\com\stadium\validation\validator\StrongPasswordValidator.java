package com.stadium.validation.validator;

import com.stadium.validation.annotation.StrongPassword;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * 强密码验证器
 */
public class StrongPasswordValidator implements ConstraintValidator<StrongPassword, String> {

    private int minLength;
    private boolean requireUppercase;
    private boolean requireLowercase;
    private boolean requireDigit;
    private boolean requireSpecialChar;

    private static final Pattern UPPERCASE_PATTERN = Pattern.compile("[A-Z]");
    private static final Pattern LOWERCASE_PATTERN = Pattern.compile("[a-z]");
    private static final Pattern DIGIT_PATTERN = Pattern.compile("[0-9]");
    private static final Pattern SPECIAL_CHAR_PATTERN = Pattern.compile("[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>/?]");

    @Override
    public void initialize(StrongPassword constraintAnnotation) {
        this.minLength = constraintAnnotation.minLength();
        this.requireUppercase = constraintAnnotation.requireUppercase();
        this.requireLowercase = constraintAnnotation.requireLowercase();
        this.requireDigit = constraintAnnotation.requireDigit();
        this.requireSpecialChar = constraintAnnotation.requireSpecialChar();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (StringUtils.isBlank(value)) {
            return true; // 空值由@NotBlank处理
        }

        // 创建自定义错误消息
        context.disableDefaultConstraintViolation();

        // 检查长度
        if (value.length() < minLength) {
            context.buildConstraintViolationWithTemplate("密码长度不能小于" + minLength + "个字符")
                    .addConstraintViolation();
            return false;
        }

        // 检查大写字母
        if (requireUppercase && !UPPERCASE_PATTERN.matcher(value).find()) {
            context.buildConstraintViolationWithTemplate("密码必须包含至少一个大写字母")
                    .addConstraintViolation();
            return false;
        }

        // 检查小写字母
        if (requireLowercase && !LOWERCASE_PATTERN.matcher(value).find()) {
            context.buildConstraintViolationWithTemplate("密码必须包含至少一个小写字母")
                    .addConstraintViolation();
            return false;
        }

        // 检查数字
        if (requireDigit && !DIGIT_PATTERN.matcher(value).find()) {
            context.buildConstraintViolationWithTemplate("密码必须包含至少一个数字")
                    .addConstraintViolation();
            return false;
        }

        // 检查特殊字符
        if (requireSpecialChar && !SPECIAL_CHAR_PATTERN.matcher(value).find()) {
            context.buildConstraintViolationWithTemplate("密码必须包含至少一个特殊字符")
                    .addConstraintViolation();
            return false;
        }

        return true;
    }
}
