package com.stadium.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 活动报名数据传输对象
 */
@Data
@Schema(description = "活动报名信息")
public class ActivityRegistrationDTO {

    @Schema(description = "报名ID")
    private Long id;

    @NotNull(message = "活动ID不能为空")
    @Schema(description = "活动ID", required = true)
    private Long activityId;

    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", required = true)
    private Long userId;

    @Schema(description = "报名时间")
    private LocalDateTime registerTime;

    @Schema(description = "报名状态：0-待审核，1-已通过，2-已拒绝，3-已取消")
    private Integer status;

    @Schema(description = "支付状态：0-未支付，1-已支付，2-已退款")
    private Integer paymentStatus;

    @Schema(description = "支付时间")
    private LocalDateTime paymentTime;

    @Schema(description = "支付金额")
    private BigDecimal paymentAmount;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "审核人ID")
    private Long auditorId;

    @Schema(description = "审核备注")
    private String auditRemark;
}