#!/bin/bash

echo "========================================"
echo "体育场馆管理系统兼容性验证"
echo "========================================"
echo

echo "正在验证Java版本..."
if command -v java &> /dev/null; then
    java -version
    JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
    if [ "$JAVA_VERSION" -ge 11 ]; then
        echo "✅ Java版本检查通过 (版本: $JAVA_VERSION)"
    else
        echo "❌ Java版本过低，需要Java 11或更高版本"
        exit 1
    fi
else
    echo "❌ Java环境未正确配置"
    exit 1
fi

echo
echo "正在验证Maven环境..."
if command -v mvn &> /dev/null; then
    mvn -version | head -n 1
    echo "✅ Maven环境检查通过"
else
    echo "❌ Maven环境未正确配置"
    exit 1
fi

echo
echo "正在检查项目结构..."
if [ ! -f "backend/pom.xml" ]; then
    echo "❌ 后端项目结构不完整"
    exit 1
fi

if [ ! -f "frontend/package.json" ]; then
    echo "❌ 前端项目结构不完整"
    exit 1
fi

echo "✅ 项目结构检查通过"

echo
echo "正在验证依赖配置..."
cd backend

echo "检查Maven依赖解析..."
if mvn dependency:resolve -q > /dev/null 2>&1; then
    echo "✅ Maven依赖解析正常"
else
    echo "⚠️  Maven依赖解析可能存在问题"
fi

echo
echo "正在验证编译兼容性..."
if mvn compile -q -DskipTests > /dev/null 2>&1; then
    echo "✅ 编译验证通过"
else
    echo "⚠️  编译可能存在问题，请检查日志"
fi

cd ..

echo
echo "========================================"
echo "兼容性验证完成"
echo "========================================"
echo
echo "如果所有检查都通过，系统应该可以正常启动"
echo "使用 ./start-all.sh 启动完整系统"
echo

# 检查是否有权限问题
if [ ! -x "start-all.sh" ]; then
    echo "正在设置启动脚本权限..."
    chmod +x start-all.sh
    echo "✅ 启动脚本权限已设置"
fi

echo "验证完成！"
