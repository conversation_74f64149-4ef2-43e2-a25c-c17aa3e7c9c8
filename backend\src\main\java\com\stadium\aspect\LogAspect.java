package com.stadium.aspect;

import com.stadium.entity.OperationLog;
import com.stadium.mapper.OperationLogMapper;
import com.stadium.util.IpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDateTime;

@Slf4j
@Aspect
@Component("operationLogAspect") // Renamed bean to avoid conflict
@RequiredArgsConstructor
public class LogAspect {

    private final OperationLogMapper operationLogMapper;

    @Around("execution(* com.stadium.controller.*.*(..))")
    public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = joinPoint.proceed();
        long endTime = System.currentTimeMillis();

        try {
            // 记录操作日志
            OperationLog operationLog = new OperationLog();
            operationLog.setUserId(getCurrentUserId());
            operationLog.setOperation(getOperationName(joinPoint));
            operationLog.setParams(getParams(joinPoint));
            operationLog.setResult(result != null ? result.toString() : null);
            operationLog.setDuration(endTime - startTime);
            operationLog.setIp(getIp());
            operationLog.setCreateTime(LocalDateTime.now());
            operationLogMapper.insert(operationLog);
        } catch (Exception e) {
            log.error("记录操作日志失败", e);
        }

        return result;
    }

    private Long getCurrentUserId() {
        try {
            return SecurityContextHolder.getContext().getAuthentication().getPrincipal() != null
                    ? Long.parseLong(SecurityContextHolder.getContext().getAuthentication().getName())
                    : null;
        } catch (Exception e) {
            return null;
        }
    }

    private String getOperationName(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        return method.getDeclaringClass().getSimpleName() + "." + method.getName();
    }

    private String getParams(ProceedingJoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        StringBuilder params = new StringBuilder();
        for (Object arg : args) {
            if (arg != null) {
                params.append(arg.toString()).append(",");
            }
        }
        return params.length() > 0 ? params.substring(0, params.length() - 1) : "";
    }

    private String getIp() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return "unknown";
        }
        try {
            HttpServletRequest request = attributes.getRequest();
            return IpUtil.getIpAddr(request);
        } catch (Exception e) {
            return "unknown";
        }
    }
}