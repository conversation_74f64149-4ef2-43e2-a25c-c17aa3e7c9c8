package com.stadium.service.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.stadium.service.CacheService;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

/**
 * 本地缓存服务实现
 * 使用Caffeine实现，提供高性能的本地缓存
 */
@Service
public class CacheServiceImpl implements CacheService {

    private Cache<String, Object> cache;

    @PostConstruct
    public void init() {
        // 初始化缓存
        cache = Caffeine.newBuilder()
                .expireAfterWrite(30, TimeUnit.MINUTES) // 写入后30分钟过期
                .expireAfterAccess(1, TimeUnit.HOURS) // 访问后1小时过期
                .initialCapacity(100) // 初始容量
                .maximumSize(1000) // 最大容量
                .recordStats() // 开启统计
                .build();
    }

    @Override
    public void set(String key, Object value) {
        cache.put(key, value);
    }

    @Override
    public void set(String key, Object value, long timeout, TimeUnit unit) {
        // Caffeine不支持单独设置每个键的过期时间
        // 这里简单实现为设置值，过期时间使用全局配置
        cache.put(key, value);
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> type) {
        return (T) cache.getIfPresent(key);
    }

    @Override
    public void delete(String key) {
        cache.invalidate(key);
    }

    @Override
    public void clear() {
        cache.invalidateAll();
    }

    @Override
    public boolean hasKey(String key) {
        return cache.getIfPresent(key) != null;
    }

    @Override
    public long getExpire(String key) {
        // Caffeine不支持获取过期时间
        // 返回-1表示不支持此操作
        return -1;
    }

    @Override
    public boolean expire(String key, long timeout, TimeUnit unit) {
        // Caffeine不支持单独设置键的过期时间
        // 返回false表示操作不支持
        return false;
    }
}