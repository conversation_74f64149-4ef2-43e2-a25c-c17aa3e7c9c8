server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: stadium-management-system
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
      - org.springframework.boot.autoconfigure.data.jpa.JpaRepositoriesAutoConfiguration
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************************
    username: root
    password: ${MYSQL_PASSWORD:18773124}
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
  # mvc:
    # Spring Boot 3.x 默认使用 PATH_PATTERN_PARSER，无需配置
    # pathmatch:
    #   matching-strategy: PATH_PATTERN_PARSER

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.stadium.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    root: info
    '[com.stadium]': debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"

# 自定义配置
stadium:
  jwt:
    secret: stadium-management-system-secret-key
    access-token-expiration: 3600
    refresh-token-expiration: 604800
  upload:
    path: uploads/
    allowed-types: jpg,jpeg,png,gif
    max-size: 10485760
  swagger:
    enabled: true
  cors:
    allowed-origins: http://localhost:8081,http://localhost:3000,http://127.0.0.1:8081,http://127.0.0.1:3000
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "*"
    exposed-headers: Authorization,Content-Type
    allow-credentials: true
    max-age: 3600

# SpringDoc配置 (启用Swagger)
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true

# 任务执行配置
task:
  execution:
    pool:
      core-size: 8
      max-size: 16
      queue-capacity: 100
      thread-name-prefix: "stadium-task-"
      allow-core-thread-timeout: true
      keep-alive: 60

# 性能监控配置
performance:
  monitor:
    enabled: true
    db-enabled: true
    method-enabled: true
    cache-enabled: true
    jvm-enabled: true

# 缓存配置
cache-config:
  local-enabled: true
  redis-enabled: true

# 加密配置
encryption:
  secret-key: stadium-management-encryption-secret-key-2024