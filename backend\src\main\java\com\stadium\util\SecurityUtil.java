package com.stadium.util;

import com.stadium.exception.BusinessException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 安全工具类
 */
public class SecurityUtil {
    private static final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    /**
     * 获取当前登录用户名
     *
     * @return 用户名
     */
    public static String getCurrentUsername() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return null;
        }

        Object principal = authentication.getPrincipal();
        if (principal instanceof UserDetails) {
            return ((UserDetails) principal).getUsername();
        } else if (principal instanceof String) {
            return (String) principal;
        }

        return null;
    }

    /**
     * 获取当前登录用户ID
     *
     * @return 用户ID
     */
    public static Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            System.out.println("SecurityUtil: 获取当前用户ID失败：未找到认证信息或未认证");
            return null;
        }

        Object principal = authentication.getPrincipal();
        System.out
                .println("SecurityUtil: Principal类型: " + (principal != null ? principal.getClass().getName() : "null"));

        // 尝试通过类名判断，避免类加载器问题
        if (principal != null && principal.getClass().getName().endsWith("CustomUserDetails")) {
            try {
                // 通过反射获取User对象
                Object user = principal.getClass().getMethod("getUser").invoke(principal);
                // 通过反射获取User的ID
                Long userId = (Long) user.getClass().getMethod("getId").invoke(user);
                System.out.println("SecurityUtil: 通过反射从CustomUserDetails获取用户ID成功: " + userId);
                return userId;
            } catch (Exception e) {
                System.out.println("SecurityUtil: 通过反射获取用户ID失败: " + e.getMessage());
                e.printStackTrace();
            }
        } else if (principal instanceof UserDetails) {
            String username = ((UserDetails) principal).getUsername();
            System.out.println("SecurityUtil: 从UserDetails获取用户名: " + username);
            try {
                Long userId = Long.parseLong(username);
                System.out.println("SecurityUtil: 从用户名解析出用户ID: " + userId);
                return userId;
            } catch (NumberFormatException e) {
                System.out.println("SecurityUtil: 无法从用户名解析出用户ID: " + username);
            }
        } else if (principal instanceof String) {
            String username = (String) principal;
            System.out.println("SecurityUtil: 从字符串获取用户名: " + username);
            try {
                Long userId = Long.parseLong(username);
                System.out.println("SecurityUtil: 从字符串解析出用户ID: " + userId);
                return userId;
            } catch (NumberFormatException e) {
                System.out.println("SecurityUtil: 无法从字符串解析出用户ID: " + username);
            }
        }

        System.out.println("SecurityUtil: 获取当前用户ID失败：未知的Principal类型");
        return null;
    }

    /**
     * 获取当前登录用户ID，如果未登录则抛出异常
     *
     * @return 用户ID
     */
    public static Long getCurrentUserIdRequired() {
        Long userId = getCurrentUserId();
        if (userId == null) {
            throw new BusinessException("用户未登录");
        }
        return userId;
    }

    /**
     * 获取当前请求的token
     */
    public static String getCurrentToken() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return null;
        }
        HttpServletRequest request = attributes.getRequest();
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            return token.substring(7);
        }
        return null;
    }

    /**
     * 获取当前请求IP
     */
    public static String getCurrentIp() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return "";
        }
        HttpServletRequest request = attributes.getRequest();
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * 密码加密
     */
    public static String encode(String password) {
        return passwordEncoder.encode(password);
    }

    /**
     * 密码匹配
     */
    public static boolean matches(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    /**
     * 生成随机密码
     *
     * @param length 密码长度
     * @return 随机密码
     */
    public static String generateRandomPassword(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+";
        StringBuilder password = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int index = (int) (Math.random() * chars.length());
            password.append(chars.charAt(index));
        }
        return password.toString();
    }
}