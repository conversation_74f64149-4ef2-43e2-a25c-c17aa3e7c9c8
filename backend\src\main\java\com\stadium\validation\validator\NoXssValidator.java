package com.stadium.validation.validator;

import com.stadium.util.InputValidator;
import com.stadium.validation.annotation.NoXss;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * 禁止XSS攻击验证器
 */
@RequiredArgsConstructor
public class NoXssValidator implements ConstraintValidator<NoXss, String> {

    private final InputValidator inputValidator;

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (StringUtils.isBlank(value)) {
            return true;
        }

        return !inputValidator.containsXssAttack(value);
    }
}
