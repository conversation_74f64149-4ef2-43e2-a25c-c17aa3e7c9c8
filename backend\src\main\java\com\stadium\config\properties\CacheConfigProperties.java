package com.stadium.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 缓存配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "cache-config")
public class CacheConfigProperties {

    /**
     * 是否启用本地缓存
     */
    private Boolean localEnabled = true;

    /**
     * 是否启用Redis缓存
     */
    private Boolean redisEnabled = true;
}
