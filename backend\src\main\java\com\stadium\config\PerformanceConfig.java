package com.stadium.config;

import com.stadium.config.properties.PerformanceProperties;
import com.stadium.config.properties.CacheConfigProperties;
import com.stadium.config.properties.TaskProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 性能配置类
 * 用于配置性能相关的Bean，如线程池、缓存等
 */
@Slf4j
@Configuration
@EnableAsync
@RequiredArgsConstructor
public class PerformanceConfig {

    private final TaskProperties taskProperties;
    private final PerformanceProperties performanceProperties;
    private final CacheConfigProperties cacheConfigProperties;

    /**
     * 配置异步任务执行器
     */
    @Bean(name = "taskExecutor")
    public Executor taskExecutor() {
        log.info("Creating Async Task Executor");

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(taskProperties.getExecution().getPool().getCoreSize());
        executor.setMaxPoolSize(taskProperties.getExecution().getPool().getMaxSize());
        executor.setQueueCapacity(taskProperties.getExecution().getPool().getQueueCapacity());
        executor.setThreadNamePrefix(taskProperties.getExecution().getPool().getThreadNamePrefix());
        executor.setAllowCoreThreadTimeOut(taskProperties.getExecution().getPool().getAllowCoreThreadTimeout());
        executor.setKeepAliveSeconds(taskProperties.getExecution().getPool().getKeepAlive());

        // 拒绝策略：调用者运行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 初始化
        executor.initialize();

        return executor;
    }

    /**
     * 初始化性能监控
     */
    @Bean
    public String performanceMonitorInitializer() {
        if (performanceProperties.getMonitor().getEnabled()) {
            log.info("Initializing Performance Monitor");

            if (performanceProperties.getMonitor().getDbEnabled()) {
                log.info("Database Performance Monitoring enabled");
                // 初始化数据库性能监控
            }

            if (performanceProperties.getMonitor().getMethodEnabled()) {
                log.info("Method Performance Monitoring enabled");
                // 初始化方法性能监控
            }

            if (performanceProperties.getMonitor().getCacheEnabled()) {
                log.info("Cache Performance Monitoring enabled");
                // 初始化缓存性能监控
            }

            if (performanceProperties.getMonitor().getJvmEnabled()) {
                log.info("JVM Performance Monitoring enabled");
                // 初始化JVM性能监控
            }

            return "Performance Monitor Initialized";
        }
        return "Performance Monitor Disabled";
    }

    /**
     * 初始化缓存配置
     */
    @Bean
    public String cacheConfigInitializer() {
        log.info("Initializing Cache Configuration");

        StringBuilder status = new StringBuilder();

        if (cacheConfigProperties.getLocalEnabled()) {
            log.info("Local Cache enabled");
            status.append("Local Cache enabled; ");
            // 初始化本地缓存
        }

        if (cacheConfigProperties.getRedisEnabled()) {
            log.info("Redis Cache enabled");
            status.append("Redis Cache enabled; ");
            // 初始化Redis缓存
        }

        return status.length() > 0 ? status.toString() : "No Cache enabled";
    }
}
