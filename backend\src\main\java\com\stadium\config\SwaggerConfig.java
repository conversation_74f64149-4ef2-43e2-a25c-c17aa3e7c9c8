package com.stadium.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * OpenAPI 3.0 配置类 (Knife4j)
 * 只在stadium.swagger.enabled=true时启用
 */
@Configuration
@ConditionalOnProperty(name = "stadium.swagger.enabled", havingValue = "true")
public class SwaggerConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("体育场馆管理系统API文档")
                        .description("提供系统管理员、场馆管理员、财务人员和普通用户的功能接口")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("Admin")
                                .url("http://localhost:8080")
                                .email("<EMAIL>")));
    }
}