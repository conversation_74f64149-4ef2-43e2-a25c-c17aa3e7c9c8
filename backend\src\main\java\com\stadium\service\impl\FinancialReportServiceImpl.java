package com.stadium.service.impl;

import com.stadium.dto.FinancialReportDTO;
import com.stadium.service.FinancialReportService;
import com.stadium.util.ExcelUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 财务报表服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinancialReportServiceImpl implements FinancialReportService {

    @Override
    public FinancialReportDTO getDailyReport(LocalDate date) {
        log.info("获取日收入报表，日期：{}", date);

        FinancialReportDTO report = new FinancialReportDTO();
        report.setReportDate(date);
        report.setReportType("DAILY");
        report.setTotalIncome(BigDecimal.valueOf(5000.00));
        report.setRefundAmount(BigDecimal.valueOf(200.00));
        report.setNetIncome(BigDecimal.valueOf(4800.00));
        report.setOrderCount(25);
        report.setRefundCount(2);

        return report;
    }

    @Override
    public FinancialReportDTO getMonthlyReport(Integer year, Integer month) {
        log.info("获取月收入报表，年份：{}，月份：{}", year, month);

        FinancialReportDTO report = new FinancialReportDTO();
        report.setReportType("MONTHLY");
        report.setTotalIncome(BigDecimal.valueOf(150000.00));
        report.setRefundAmount(BigDecimal.valueOf(6000.00));
        report.setNetIncome(BigDecimal.valueOf(144000.00));
        report.setOrderCount(750);
        report.setRefundCount(60);

        return report;
    }

    @Override
    public FinancialReportDTO getYearlyReport(Integer year) {
        log.info("获取年收入报表，年份：{}", year);

        FinancialReportDTO report = new FinancialReportDTO();
        report.setReportType("YEARLY");
        report.setTotalIncome(BigDecimal.valueOf(1800000.00));
        report.setRefundAmount(BigDecimal.valueOf(72000.00));
        report.setNetIncome(BigDecimal.valueOf(1728000.00));
        report.setOrderCount(9000);
        report.setRefundCount(720);

        return report;
    }

    @Override
    public FinancialReportDTO getCustomReport(LocalDate startDate, LocalDate endDate) {
        log.info("获取自定义时间段报表，开始日期：{}，结束日期：{}", startDate, endDate);

        FinancialReportDTO report = new FinancialReportDTO();
        report.setReportType("CUSTOM");
        report.setTotalIncome(BigDecimal.valueOf(25000.00));
        report.setRefundAmount(BigDecimal.valueOf(1000.00));
        report.setNetIncome(BigDecimal.valueOf(24000.00));
        report.setOrderCount(125);
        report.setRefundCount(10);

        return report;
    }

    @Override
    public void exportDailyReport(LocalDate date, HttpServletResponse response) throws IOException {
        log.info("导出日报表，日期：{}", date);

        // 获取日报表数据
        FinancialReportDTO report = getDailyReport(date);

        // 准备Excel数据
        String title = "日收入报表 - " + date;
        List<String> headers = List.of("项目", "金额(元)", "数量", "备注");

        List<List<Object>> data = new ArrayList<>();
        data.add(List.of("总收入", report.getTotalIncome(), report.getOrderCount(), "当日总订单收入"));
        data.add(List.of("退款金额", report.getRefundAmount(), report.getRefundCount(), "当日退款总额"));
        data.add(List.of("净收入", report.getNetIncome(), "-", "总收入减去退款"));

        // 导出Excel
        String fileName = "daily_report_" + date + ".xlsx";
        ExcelUtils.exportFinancialReport(title, headers, data, response, fileName);
    }

    @Override
    public void exportMonthlyReport(Integer year, Integer month, HttpServletResponse response) throws IOException {
        log.info("导出月报表，年份：{}，月份：{}", year, month);

        // 获取月报表数据
        FinancialReportDTO report = getMonthlyReport(year, month);

        // 准备Excel数据
        String title = "月收入报表 - " + year + "年" + month + "月";
        List<String> headers = List.of("项目", "金额(元)", "数量", "备注");

        List<List<Object>> data = new ArrayList<>();
        data.add(List.of("总收入", report.getTotalIncome(), report.getOrderCount(), "当月总订单收入"));
        data.add(List.of("退款金额", report.getRefundAmount(), report.getRefundCount(), "当月退款总额"));
        data.add(List.of("净收入", report.getNetIncome(), "-", "总收入减去退款"));

        // 导出Excel
        String fileName = "monthly_report_" + year + "_" + month + ".xlsx";
        ExcelUtils.exportFinancialReport(title, headers, data, response, fileName);
    }

    @Override
    public void exportYearlyReport(Integer year, HttpServletResponse response) throws IOException {
        log.info("导出年报表，年份：{}", year);

        // 获取年报表数据
        FinancialReportDTO report = getYearlyReport(year);

        // 准备Excel数据
        String title = "年收入报表 - " + year + "年";
        List<String> headers = List.of("项目", "金额(元)", "数量", "备注");

        List<List<Object>> data = new ArrayList<>();
        data.add(List.of("总收入", report.getTotalIncome(), report.getOrderCount(), "当年总订单收入"));
        data.add(List.of("退款金额", report.getRefundAmount(), report.getRefundCount(), "当年退款总额"));
        data.add(List.of("净收入", report.getNetIncome(), "-", "总收入减去退款"));

        // 导出Excel
        String fileName = "yearly_report_" + year + ".xlsx";
        ExcelUtils.exportFinancialReport(title, headers, data, response, fileName);
    }

    @Override
    public void exportCustomReport(LocalDate startDate, LocalDate endDate, HttpServletResponse response)
            throws IOException {
        log.info("导出自定义时间段报表，开始日期：{}，结束日期：{}", startDate, endDate);

        // 获取自定义时间段报表数据
        FinancialReportDTO report = getCustomReport(startDate, endDate);

        // 准备Excel数据
        String title = "自定义时间段收入报表 - " + startDate + " 至 " + endDate;
        List<String> headers = List.of("项目", "金额(元)", "数量", "备注");

        List<List<Object>> data = new ArrayList<>();
        data.add(List.of("总收入", report.getTotalIncome(), report.getOrderCount(), "时间段内总订单收入"));
        data.add(List.of("退款金额", report.getRefundAmount(), report.getRefundCount(), "时间段内退款总额"));
        data.add(List.of("净收入", report.getNetIncome(), "-", "总收入减去退款"));

        // 导出Excel
        String fileName = "custom_report_" + startDate + "_" + endDate + ".xlsx";
        ExcelUtils.exportFinancialReport(title, headers, data, response, fileName);
    }

    @Override
    public List<Map<String, Object>> getIncomeByPaymentMethod(LocalDate startDate, LocalDate endDate) {
        log.info("获取按支付方式分类的收入统计，开始日期：{}，结束日期：{}", startDate, endDate);

        List<Map<String, Object>> result = new ArrayList<>();

        Map<String, Object> alipay = new HashMap<>();
        alipay.put("paymentMethod", "支付宝");
        alipay.put("amount", BigDecimal.valueOf(15000.00));
        alipay.put("count", 75);
        result.add(alipay);

        Map<String, Object> wechat = new HashMap<>();
        wechat.put("paymentMethod", "微信支付");
        wechat.put("amount", BigDecimal.valueOf(8000.00));
        wechat.put("count", 40);
        result.add(wechat);

        Map<String, Object> cash = new HashMap<>();
        cash.put("paymentMethod", "现金");
        cash.put("amount", BigDecimal.valueOf(2000.00));
        cash.put("count", 10);
        result.add(cash);

        return result;
    }

    @Override
    public List<Map<String, Object>> getIncomeByVenue(LocalDate startDate, LocalDate endDate) {
        log.info("获取按场馆分类的收入统计，开始日期：{}，结束日期：{}", startDate, endDate);

        List<Map<String, Object>> result = new ArrayList<>();

        Map<String, Object> basketball = new HashMap<>();
        basketball.put("venueName", "篮球场A");
        basketball.put("amount", BigDecimal.valueOf(12000.00));
        basketball.put("count", 60);
        result.add(basketball);

        Map<String, Object> tennis = new HashMap<>();
        tennis.put("venueName", "网球场B");
        tennis.put("amount", BigDecimal.valueOf(8000.00));
        tennis.put("count", 40);
        result.add(tennis);

        Map<String, Object> swimming = new HashMap<>();
        swimming.put("venueName", "游泳池C");
        swimming.put("amount", BigDecimal.valueOf(5000.00));
        swimming.put("count", 25);
        result.add(swimming);

        return result;
    }

    @Override
    public List<Map<String, Object>> getIncomeByDate(LocalDate startDate, LocalDate endDate, String groupBy) {
        log.info("获取按日期分组的收入统计，开始日期：{}，结束日期：{}，分组类型：{}", startDate, endDate, groupBy);

        List<Map<String, Object>> result = new ArrayList<>();

        for (int i = 0; i < 7; i++) {
            Map<String, Object> data = new HashMap<>();
            data.put("date", startDate.plusDays(i));
            data.put("amount", BigDecimal.valueOf(3000.00 + i * 500));
            data.put("count", 15 + i * 5);
            result.add(data);
        }

        return result;
    }

    @Override
    public List<Map<String, Object>> getIncomeTrend(LocalDate startDate, LocalDate endDate) {
        log.info("获取收入趋势，开始日期：{}，结束日期：{}", startDate, endDate);

        List<Map<String, Object>> result = new ArrayList<>();

        for (int i = 0; i < 30; i++) {
            Map<String, Object> data = new HashMap<>();
            data.put("date", startDate.plusDays(i));
            data.put("income", BigDecimal.valueOf(2000.00 + Math.random() * 3000));
            data.put("orders", (int) (10 + Math.random() * 20));
            result.add(data);
        }

        return result;
    }

    @Override
    public Map<String, Object> getRefundStatistics(LocalDate startDate, LocalDate endDate) {
        log.info("获取退款统计，开始日期：{}，结束日期：{}", startDate, endDate);

        Map<String, Object> result = new HashMap<>();
        result.put("totalRefundAmount", BigDecimal.valueOf(5000.00));
        result.put("totalRefundCount", 50);
        result.put("refundRate", BigDecimal.valueOf(0.05));
        result.put("avgRefundAmount", BigDecimal.valueOf(100.00));

        return result;
    }

    @Override
    public String generateReconciliation(LocalDate date) {
        log.info("生成对账单，日期：{}", date);

        try {
            // 获取当日财务数据
            FinancialReportDTO report = getDailyReport(date);

            // 创建对账单目录
            String reconciliationDir = "/uploads/reconciliation/";
            java.io.File dir = new java.io.File(reconciliationDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 生成对账单文件名
            String fileName = "reconciliation_" + date + ".txt";
            String filePath = reconciliationDir + fileName;

            // 生成对账单内容
            StringBuilder content = new StringBuilder();
            content.append("===========================================\n");
            content.append("           体育场馆管理系统对账单\n");
            content.append("===========================================\n");
            content.append("对账日期: ").append(date).append("\n");
            content.append("生成时间: ").append(java.time.LocalDateTime.now()).append("\n");
            content.append("-------------------------------------------\n");
            content.append("财务汇总:\n");
            content.append("总收入: ¥").append(report.getTotalIncome()).append("\n");
            content.append("退款金额: ¥").append(report.getRefundAmount()).append("\n");
            content.append("净收入: ¥").append(report.getNetIncome()).append("\n");
            content.append("订单数量: ").append(report.getOrderCount()).append("笔\n");
            content.append("退款数量: ").append(report.getRefundCount()).append("笔\n");
            content.append("-------------------------------------------\n");
            content.append("备注: 本对账单由系统自动生成\n");
            content.append("===========================================\n");

            // 写入文件
            try (java.io.FileWriter writer = new java.io.FileWriter(filePath)) {
                writer.write(content.toString());
            }

            log.info("对账单生成完成，文件路径：{}", filePath);
            return filePath;

        } catch (Exception e) {
            log.error("生成对账单失败，日期：{}", date, e);
            throw new RuntimeException("生成对账单失败: " + e.getMessage(), e);
        }
    }
}
