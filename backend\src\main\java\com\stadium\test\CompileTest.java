package com.stadium.test;

import com.stadium.entity.Activity;
import com.stadium.common.api.ApiResult;
import com.stadium.dto.ActivityDTO;
import org.springframework.stereotype.Component;

/**
 * 编译测试类
 */
@Component
public class CompileTest {
    
    public ApiResult<Activity> test() {
        Activity activity = new Activity();
        activity.setName("测试活动");
        return ApiResult.success(activity);
    }
    
    public ActivityDTO testDto() {
        ActivityDTO dto = new ActivityDTO();
        dto.setName("测试DTO");
        return dto;
    }
}
