package com.stadium.common.exception;

import com.stadium.common.api.ApiResult;
import com.stadium.util.ErrorAnalyzer;
import com.stadium.util.HtmlUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.NoHandlerFoundException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
@RequiredArgsConstructor
public class GlobalExceptionHandler {

    private final ErrorAnalyzer errorAnalyzer;
    private final HtmlUtils htmlUtils;

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<Void> handleBusinessException(BusinessException e, HttpServletRequest request) {
        log.error("业务异常: {}, 请求URL: {}", e.getMessage(), request.getRequestURI(), e);
        errorAnalyzer.recordError("BusinessException", e.getMessage(), e);
        return ApiResult.failed(e.getCode(), e.getMessage());
    }

    /**
     * 处理参数校验异常（@Valid注解）
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<Map<String, String>> handleMethodArgumentNotValidException(MethodArgumentNotValidException e,
            HttpServletRequest request) {
        BindingResult bindingResult = e.getBindingResult();
        Map<String, String> errors = new HashMap<>();

        if (bindingResult.hasErrors()) {
            List<FieldError> fieldErrors = bindingResult.getFieldErrors();
            errors = fieldErrors.stream()
                    .collect(Collectors.toMap(
                            FieldError::getField,
                            error -> htmlUtils.escapeHtml(error.getDefaultMessage()),
                            (existing, replacement) -> existing + "; " + replacement));
        }

        String message = "参数校验失败";
        if (!errors.isEmpty()) {
            message = errors.values().iterator().next();
        }

        log.error("参数校验异常: {}, 请求URL: {}, 详细错误: {}", message, request.getRequestURI(), errors);
        errorAnalyzer.recordError("ValidationException", message, e);

        return ApiResult.validateFailed(message, errors);
    }

    /**
     * 处理参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<Map<String, String>> handleBindException(BindException e, HttpServletRequest request) {
        BindingResult bindingResult = e.getBindingResult();
        Map<String, String> errors = new HashMap<>();

        if (bindingResult.hasErrors()) {
            List<FieldError> fieldErrors = bindingResult.getFieldErrors();
            errors = fieldErrors.stream()
                    .collect(Collectors.toMap(
                            FieldError::getField,
                            error -> htmlUtils.escapeHtml(error.getDefaultMessage()),
                            (existing, replacement) -> existing + "; " + replacement));
        }

        String message = "参数绑定失败";
        if (!errors.isEmpty()) {
            message = errors.values().iterator().next();
        }

        log.error("参数绑定异常: {}, 请求URL: {}, 详细错误: {}", message, request.getRequestURI(), errors);
        errorAnalyzer.recordError("BindException", message, e);

        return ApiResult.validateFailed(message, errors);
    }

    /**
     * 处理约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<Map<String, String>> handleConstraintViolationException(ConstraintViolationException e,
            HttpServletRequest request) {
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        Map<String, String> errors = new HashMap<>();

        if (violations != null && !violations.isEmpty()) {
            errors = violations.stream()
                    .collect(Collectors.toMap(
                            violation -> {
                                String propertyPath = violation.getPropertyPath().toString();
                                return propertyPath.substring(propertyPath.lastIndexOf('.') + 1);
                            },
                            violation -> htmlUtils.escapeHtml(violation.getMessage()),
                            (existing, replacement) -> existing + "; " + replacement));
        }

        String message = "参数约束违反";
        if (!errors.isEmpty()) {
            message = errors.values().iterator().next();
        }

        log.error("参数约束违反: {}, 请求URL: {}, 详细错误: {}", message, request.getRequestURI(), errors);
        errorAnalyzer.recordError("ConstraintViolationException", message, e);

        return ApiResult.validateFailed(message, errors);
    }

    /**
     * 处理请求参数缺失异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<Void> handleMissingServletRequestParameterException(MissingServletRequestParameterException e,
            HttpServletRequest request) {
        String message = "缺少请求参数: " + e.getParameterName();
        log.error("{}, 请求URL: {}", message, request.getRequestURI(), e);
        errorAnalyzer.recordError("MissingParameterException", message, e);
        return ApiResult.validateFailed(message);
    }

    /**
     * 处理参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<Void> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e,
            HttpServletRequest request) {
        String message;
        if (e.getRequiredType() != null) {
            message = "参数类型不匹配: " + e.getName() + "，期望类型: " + e.getRequiredType().getSimpleName();
        } else {
            message = "参数类型不匹配: " + e.getName();
        }
        log.error("{}, 请求URL: {}", message, request.getRequestURI(), e);
        errorAnalyzer.recordError("TypeMismatchException", message, e);
        return ApiResult.validateFailed(message);
    }

    /**
     * 处理HTTP请求方法不支持异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public ApiResult<Void> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e,
            HttpServletRequest request) {
        String message = "不支持的HTTP方法: " + e.getMethod();
        log.error("{}, 请求URL: {}", message, request.getRequestURI(), e);
        errorAnalyzer.recordError("MethodNotAllowedException", message, e);
        return ApiResult.failed(405, message);
    }

    /**
     * 处理请求体解析异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<Void> handleHttpMessageNotReadableException(HttpMessageNotReadableException e,
            HttpServletRequest request) {
        String message = "请求体解析失败";
        log.error("{}, 请求URL: {}", message, request.getRequestURI(), e);
        errorAnalyzer.recordError("MessageNotReadableException", message, e);
        return ApiResult.validateFailed(message);
    }

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<Void> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e,
            HttpServletRequest request) {
        String message = "上传文件大小超过限制";
        log.error("{}, 请求URL: {}", message, request.getRequestURI(), e);
        errorAnalyzer.recordError("MaxUploadSizeExceededException", message, e);
        return ApiResult.validateFailed(message);
    }

    /**
     * 处理接口不存在异常
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ApiResult<Void> handleNoHandlerFoundException(NoHandlerFoundException e, HttpServletRequest request) {
        String message = "接口不存在: " + e.getRequestURL();
        log.error("{}, 请求方法: {}", message, e.getHttpMethod(), e);
        errorAnalyzer.recordError("NoHandlerFoundException", message, e);
        return ApiResult.failed(404, message);
    }

    /**
     * 处理权限不足异常
     */
    @ExceptionHandler(AccessDeniedException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public ApiResult<Void> handleAccessDeniedException(AccessDeniedException e, HttpServletRequest request) {
        String message = "权限不足";
        log.error("{}, 请求URL: {}", message, request.getRequestURI(), e);
        errorAnalyzer.recordError("AccessDeniedException", message, e);
        return ApiResult.forbidden();
    }

    /**
     * 处理自定义禁止访问异常
     */
    @ExceptionHandler(ForbiddenException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public ApiResult<Void> handleForbiddenException(ForbiddenException e, HttpServletRequest request) {
        String message = e.getMessage();
        log.error("{}, 请求URL: {}", message, request.getRequestURI(), e);
        errorAnalyzer.recordError("ForbiddenException", message, e);
        return ApiResult.forbidden(message);
    }

    /**
     * 处理自定义未授权异常
     */
    @ExceptionHandler(UnauthorizedException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public ApiResult<Void> handleUnauthorizedException(UnauthorizedException e, HttpServletRequest request) {
        String message = e.getMessage();
        log.error("{}, 请求URL: {}", message, request.getRequestURI(), e);
        errorAnalyzer.recordError("UnauthorizedException", message, e);
        return ApiResult.unauthorized(message);
    }

    /**
     * 处理认证异常
     */
    @ExceptionHandler(AuthenticationException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public ApiResult<Void> handleAuthenticationException(AuthenticationException e, HttpServletRequest request) {
        String message;
        if (e instanceof BadCredentialsException) {
            message = "用户名或密码错误";
        } else if (e instanceof DisabledException) {
            message = "账号已被禁用";
        } else if (e instanceof LockedException) {
            message = "账号已被锁定";
        } else {
            message = "认证失败: " + e.getMessage();
        }

        log.error("{}, 请求URL: {}", message, request.getRequestURI(), e);
        errorAnalyzer.recordError("AuthenticationException", message, e);
        return ApiResult.unauthorized();
    }

    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResult<Void> handleException(Exception e, HttpServletRequest request) {
        String message = "系统异常: " + e.getMessage();
        log.error("{}, 请求URL: {}", message, request.getRequestURI(), e);
        errorAnalyzer.recordError("SystemException", message, e);
        return ApiResult.failed("系统异常，请联系管理员");
    }
}