package com.stadium.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stadium.common.api.ApiResult;
import com.stadium.entity.Announcement;
import com.stadium.service.AnnouncementService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 公告控制器
 */
@RestController
@RequestMapping("/api/announcements")
@RequiredArgsConstructor
public class AnnouncementController {

    private final AnnouncementService announcementService;

    /**
     * 分页查询公告列表
     */
    @GetMapping
    @PreAuthorize("hasAuthority('announcement:list')")
    public ApiResult<Page<Announcement>> page(
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) Integer type,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Long venueId) {
        return ApiResult.success(announcementService.page(pageNum, pageSize, type, status, venueId));
    }

    /**
     * 获取公告详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('announcement:get')")
    public ApiResult<Announcement> getById(@PathVariable Long id) {
        return ApiResult.success(announcementService.getById(id));
    }

    /**
     * 创建公告
     */
    @PostMapping
    @PreAuthorize("hasAuthority('announcement:create')")
    public ApiResult<Boolean> create(@RequestBody @Valid Announcement announcement) {
        return ApiResult.success(announcementService.create(announcement));
    }

    /**
     * 更新公告
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('announcement:update')")
    public ApiResult<Boolean> update(
            @PathVariable Long id,
            @RequestBody @Valid Announcement announcement) {
        announcement.setId(id);
        return ApiResult.success(announcementService.update(announcement));
    }

    /**
     * 删除公告
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('announcement:delete')")
    public ApiResult<Boolean> delete(@PathVariable Long id) {
        return ApiResult.success(announcementService.delete(id));
    }

    /**
     * 发布公告
     */
    @PostMapping("/{id}/publish")
    @PreAuthorize("hasAuthority('announcement:update')")
    public ApiResult<Boolean> publish(@PathVariable Long id) {
        return ApiResult.success(announcementService.publish(id));
    }

    /**
     * 下线公告
     */
    @PostMapping("/{id}/offline")
    @PreAuthorize("hasAuthority('announcement:update')")
    public ApiResult<Boolean> offline(@PathVariable Long id) {
        return ApiResult.success(announcementService.offline(id));
    }

    /**
     * 置顶公告
     */
    @PostMapping("/{id}/top")
    @PreAuthorize("hasAuthority('announcement:update')")
    public ApiResult<Boolean> setTop(@PathVariable Long id) {
        return ApiResult.success(announcementService.setTop(id));
    }

    /**
     * 取消置顶
     */
    @PostMapping("/{id}/cancel-top")
    @PreAuthorize("hasAuthority('announcement:update')")
    public ApiResult<Boolean> cancelTop(@PathVariable Long id) {
        return ApiResult.success(announcementService.cancelTop(id));
    }

    /**
     * 获取当前有效的系统公告列表（无需权限，供前台使用）
     */
    @GetMapping("/system/active")
    public ApiResult<List<Announcement>> getActiveSystemAnnouncements() {
        return ApiResult.success(announcementService.getActiveSystemAnnouncements());
    }

    /**
     * 获取当前有效的场馆公告列表（无需权限，供前台使用）
     */
    @GetMapping("/venue/{venueId}/active")
    public ApiResult<List<Announcement>> getActiveVenueAnnouncements(@PathVariable Long venueId) {
        return ApiResult.success(announcementService.getActiveVenueAnnouncements(venueId));
    }
}