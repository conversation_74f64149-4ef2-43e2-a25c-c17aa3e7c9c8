package com.stadium.validation.annotation;

import com.stadium.validation.validator.SafeHtmlValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * 安全HTML注解，允许部分安全的HTML标签
 */
@Documented
@Constraint(validatedBy = SafeHtmlValidator.class)
@Target({ ElementType.FIELD, ElementType.PARAMETER })
@Retention(RetentionPolicy.RUNTIME)
public @interface SafeHtml {

    String message() default "包含不安全的HTML内容";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    /**
     * 允许的HTML标签
     */
    String[] allowedTags() default { "p", "br", "b", "i", "u", "strong", "em", "span", "ul", "ol", "li", "a", "h1",
            "h2", "h3", "h4", "h5", "h6", "img", "table", "tr", "td", "th", "tbody", "thead" };

    /**
     * 允许的HTML属性
     */
    String[] allowedAttributes() default { "href", "src", "alt", "title", "class", "id", "target", "width", "height",
            "style" };
}
