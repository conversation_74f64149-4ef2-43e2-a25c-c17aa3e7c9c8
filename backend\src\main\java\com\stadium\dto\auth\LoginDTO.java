package com.stadium.dto.auth;

import com.stadium.validation.annotation.NoSqlInjection;
import com.stadium.validation.annotation.NoXss;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 登录请求DTO
 */
@Data
@Schema(description = "用户登录信息")
public class LoginDTO {
    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 4, max = 16, message = "用户名长度必须在4-16位之间")
    @NoXss(message = "用户名包含不安全的字符")
    @NoSqlInjection(message = "用户名包含不安全的字符")
    @Schema(description = "用户名", required = true)
    private String username;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @Size(min = 8, max = 32, message = "密码长度必须在8-32位之间")
    @Schema(description = "密码", required = true)
    private String password;

    /**
     * 验证码
     */
    @Schema(description = "验证码")
    private String captcha;

    /**
     * 验证码key
     */
    @Schema(description = "验证码key")
    private String captchaKey;

    /**
     * 记住我
     */
    @Schema(description = "记住我")
    private Boolean rememberMe;
}