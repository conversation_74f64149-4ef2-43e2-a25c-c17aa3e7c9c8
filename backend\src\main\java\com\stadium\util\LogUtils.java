package com.stadium.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 日志工具类，用于统一记录日志
 */
@Slf4j
@Component
public class LogUtils {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final int MAX_PARAM_LENGTH = 1000;
    private static final int MAX_RESULT_LENGTH = 1000;

    /**
     * 记录API请求日志
     *
     * @param request 请求对象
     * @param params  请求参数
     * @param result  响应结果
     * @param time    执行时间（毫秒）
     */
    public void logApiRequest(HttpServletRequest request, Object params, Object result, long time) {
        String requestURI = request.getRequestURI();
        String method = request.getMethod();
        String ip = IpUtil.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");
        String username = request.getRemoteUser();

        String paramsStr = formatParams(params);
        String resultStr = formatResult(result);

        log.info("API请求 - URI: {}, 方法: {}, IP: {}, 用户: {}, 参数: {}, 结果: {}, 耗时: {}ms, User-Agent: {}",
                requestURI, method, ip, username, paramsStr, resultStr, time, userAgent);
    }

    /**
     * 记录业务操作日志
     *
     * @param module    模块名称
     * @param operation 操作类型
     * @param username  操作用户
     * @param params    操作参数
     * @param result    操作结果
     * @param time      执行时间（毫秒）
     */
    public void logBusinessOperation(String module, String operation, String username, Object params, Object result,
            long time) {
        String paramsStr = formatParams(params);
        String resultStr = formatResult(result);

        log.info("业务操作 - 模块: {}, 操作: {}, 用户: {}, 参数: {}, 结果: {}, 耗时: {}ms, 时间: {}",
                module, operation, username, paramsStr, resultStr, time,
                LocalDateTime.now().format(DATE_TIME_FORMATTER));
    }

    /**
     * 记录系统错误日志
     *
     * @param module    模块名称
     * @param operation 操作类型
     * @param username  操作用户
     * @param params    操作参数
     * @param e         异常信息
     */
    public void logSystemError(String module, String operation, String username, Object params, Throwable e) {
        String paramsStr = formatParams(params);
        String errorMessage = e.getMessage();
        String stackTrace = Arrays.stream(e.getStackTrace())
                .limit(5)
                .map(StackTraceElement::toString)
                .collect(Collectors.joining("\n"));

        log.error("系统错误 - 模块: {}, 操作: {}, 用户: {}, 参数: {}, 错误: {}, 堆栈: {}, 时间: {}",
                module, operation, username, paramsStr, errorMessage, stackTrace,
                LocalDateTime.now().format(DATE_TIME_FORMATTER));
    }

    /**
     * 记录安全相关日志
     *
     * @param module    模块名称
     * @param operation 操作类型
     * @param username  操作用户
     * @param ip        IP地址
     * @param result    操作结果
     */
    public void logSecurityEvent(String module, String operation, String username, String ip, String result) {
        log.info("安全事件 - 模块: {}, 操作: {}, 用户: {}, IP: {}, 结果: {}, 时间: {}",
                module, operation, username, ip, result, LocalDateTime.now().format(DATE_TIME_FORMATTER));
    }

    /**
     * 记录数据库操作日志
     *
     * @param operation 操作类型（INSERT, UPDATE, DELETE, SELECT）
     * @param table     表名
     * @param params    操作参数
     * @param result    影响行数或查询结果数量
     * @param time      执行时间（毫秒）
     */
    public void logDatabaseOperation(String operation, String table, Object params, int result, long time) {
        String paramsStr = formatParams(params);

        log.info("数据库操作 - 操作: {}, 表: {}, 参数: {}, 影响行数: {}, 耗时: {}ms, 时间: {}",
                operation, table, paramsStr, result, time, LocalDateTime.now().format(DATE_TIME_FORMATTER));
    }

    /**
     * 格式化请求参数
     *
     * @param params 请求参数
     * @return 格式化后的参数字符串
     */
    private String formatParams(Object params) {
        if (params == null) {
            return "null";
        }

        String paramsStr;
        if (params instanceof Map) {
            // 处理Map类型参数
            @SuppressWarnings("unchecked")
            Map<String, Object> paramsMap = (Map<String, Object>) params;
            paramsStr = paramsMap.entrySet().stream()
                    .map(entry -> entry.getKey() + "=" + formatValue(entry.getValue()))
                    .collect(Collectors.joining(", "));
        } else {
            // 处理其他类型参数
            paramsStr = params.toString();
        }

        // 截断过长的参数
        if (paramsStr.length() > MAX_PARAM_LENGTH) {
            paramsStr = paramsStr.substring(0, MAX_PARAM_LENGTH) + "...";
        }

        return paramsStr;
    }

    /**
     * 格式化响应结果
     *
     * @param result 响应结果
     * @return 格式化后的结果字符串
     */
    private String formatResult(Object result) {
        if (result == null) {
            return "null";
        }

        String resultStr = result.toString();

        // 截断过长的结果
        if (resultStr.length() > MAX_RESULT_LENGTH) {
            resultStr = resultStr.substring(0, MAX_RESULT_LENGTH) + "...";
        }

        return resultStr;
    }

    /**
     * 格式化参数值，处理敏感信息
     *
     * @param value 参数值
     * @return 格式化后的参数值
     */
    private String formatValue(Object value) {
        if (value == null) {
            return "null";
        }

        String valueStr = value.toString();

        // 处理敏感信息
        if (valueStr.contains("password") || valueStr.contains("密码") ||
                valueStr.contains("token") || valueStr.contains("secret")) {
            return "******";
        }

        return valueStr;
    }
}
