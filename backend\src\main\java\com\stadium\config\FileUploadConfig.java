package com.stadium.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.lang.NonNull;

import jakarta.annotation.PostConstruct;
import java.io.File;

/**
 * 文件上传配置类
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "upload")
public class FileUploadConfig implements WebMvcConfigurer {
    /**
     * 上传文件保存路径
     */
    private String path = "uploads/";

    /**
     * 允许的文件类型，多个用逗号分隔
     */
    private String allowedTypes = "jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx";

    /**
     * 最大文件大小
     */
    private String maxSize = "10MB";

    /**
     * 图片保存路径
     */
    private String imagePath = "images/";

    /**
     * 头像保存路径
     */
    private String avatarPath = "avatars/";

    @PostConstruct
    public void init() {
        // 确保上传目录存在
        createDirectoryIfNotExists(path);
        createDirectoryIfNotExists(path + imagePath);
        createDirectoryIfNotExists(path + avatarPath);
        createDirectoryIfNotExists(path + "documents/");
        createDirectoryIfNotExists(path + "exports/");
        createDirectoryIfNotExists(path + "reconciliation/");

        log.info("文件上传目录初始化完成：{}", path);
    }

    private void createDirectoryIfNotExists(String dirPath) {
        File dir = new File(dirPath);
        if (!dir.exists()) {
            boolean created = dir.mkdirs();
            if (created) {
                log.info("创建目录：{}", dirPath);
            } else {
                log.warn("创建目录失败：{}", dirPath);
            }
        }
    }

    @Override
    public void addResourceHandlers(@NonNull ResourceHandlerRegistry registry) {
        // 配置文件上传目录的访问映射
        registry.addResourceHandler("/uploads/**")
                .addResourceLocations("file:uploads/");
    }

    /**
     * 获取图片保存路径
     */
    public String getImagePath() {
        return imagePath;
    }

    /**
     * 获取头像保存路径
     */
    public String getAvatarPath() {
        return avatarPath;
    }

    /**
     * 获取基础路径
     */
    public String getBasePath() {
        return path;
    }

    /**
     * 获取最大文件大小（字节）
     */
    public long getMaxSize() {
        String size = this.maxSize.toLowerCase();
        if (size.endsWith("kb")) {
            return Long.parseLong(size.substring(0, size.length() - 2)) * 1024;
        } else if (size.endsWith("mb")) {
            return Long.parseLong(size.substring(0, size.length() - 2)) * 1024 * 1024;
        } else if (size.endsWith("gb")) {
            return Long.parseLong(size.substring(0, size.length() - 2)) * 1024 * 1024 * 1024;
        }
        return Long.parseLong(size);
    }
}