package com.stadium.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stadium.annotation.RequirePermission;
import com.stadium.common.api.ApiResult;
import com.stadium.entity.User;
import com.stadium.entity.request.UserLoginRequest;
import com.stadium.entity.request.UserRegisterRequest;
import com.stadium.entity.request.UserUpdateRequest;
import com.stadium.entity.response.UserLoginResponse;
import com.stadium.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 用户控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
@Tag(name = "用户管理", description = "用户相关接口")
public class UserController {

    private final UserService userService;

    @PostMapping("/register")
    @Operation(summary = "用户注册")
    public ApiResult<Long> register(@RequestBody @Valid UserRegisterRequest request) {
        return ApiResult.success(userService.register(request));
    }

    @PostMapping("/login")
    @Operation(summary = "用户登录")
    public ApiResult<UserLoginResponse> login(@RequestBody @Valid UserLoginRequest request) {
        return ApiResult.success(userService.login(request));
    }

    @PostMapping("/logout")
    @Operation(summary = "用户登出")
    @RequirePermission("user:logout")
    public ApiResult<Void> logout() {
        userService.logout();
        return ApiResult.success();
    }

    @GetMapping("/info")
    @Operation(summary = "获取当前用户信息")
    @RequirePermission("user:info")
    public ApiResult<User> getCurrentUser() {
        return ApiResult.success(userService.getCurrentUser());
    }

    @PutMapping("/password")
    @Operation(summary = "修改密码")
    @RequirePermission("user:password:update")
    public ApiResult<Void> updatePassword(@RequestBody @Valid UserUpdateRequest.Password request) {
        User currentUser = userService.getCurrentUser();
        if (currentUser == null) {
            return ApiResult.failed("用户未登录或会话已过期");
        }
        userService.updatePassword(currentUser.getId(), request.getOldPassword(),
                request.getNewPassword());
        return ApiResult.success();
    }

    @PutMapping("/{id}/password/reset")
    @Operation(summary = "重置密码")
    @RequirePermission("user:password:reset")
    public ApiResult<Void> resetPassword(
            @Parameter(description = "用户ID") @PathVariable Long id,
            @RequestBody @Valid UserUpdateRequest.PasswordReset request) {
        userService.resetPassword(id, request.getPassword());
        return ApiResult.success();
    }

    @PutMapping("/{id}/status")
    @Operation(summary = "更新用户状态")
    @RequirePermission("user:status:update")
    public ApiResult<Void> updateStatus(
            @Parameter(description = "用户ID") @PathVariable Long id,
            @RequestBody @Valid UserUpdateRequest.Status request) {
        userService.updateStatus(id, request.getStatus());
        return ApiResult.success();
    }

    @PutMapping("/{id}/role")
    @Operation(summary = "更新用户角色")
    @RequirePermission("user:role:update")
    public ApiResult<Void> updateRole(
            @Parameter(description = "用户ID") @PathVariable Long id,
            @RequestBody @Valid UserUpdateRequest.Role request) {
        userService.updateRole(id, request.getRole());
        return ApiResult.success();
    }

    @GetMapping("/page")
    @Operation(summary = "分页查询用户列表")
    @RequirePermission("user:list")
    public ApiResult<Page<User>> page(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页条数") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "用户名") @RequestParam(required = false) String username,
            @Parameter(description = "昵称") @RequestParam(required = false) String nickname,
            @Parameter(description = "手机号") @RequestParam(required = false) String phone,
            @Parameter(description = "邮箱") @RequestParam(required = false) String email,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status,
            @Parameter(description = "角色") @RequestParam(required = false) String role) {
        return ApiResult
                .success(userService.page(new Page<>(current, size), username, nickname, phone, email, status, role));
    }

    @Operation(summary = "刷新令牌")
    @PostMapping("/refresh-token")
    public ApiResult<String> refreshToken(
            @Parameter(description = "刷新令牌") @RequestParam String refreshToken) {
        return ApiResult.success(userService.refreshToken(refreshToken));
    }

    @Operation(summary = "更新用户信息")
    @PutMapping("/{userId}")
    public ApiResult<Void> updateUserInfo(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @RequestBody User user) {
        userService.updateUserInfo(userId, user);
        return ApiResult.success();
    }

    @Operation(summary = "更新会员等级")
    @PutMapping("/{userId}/member-level")
    public ApiResult<Void> updateMemberLevel(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "会员等级") @RequestParam Integer level,
            @Parameter(description = "购买月数") @RequestParam(required = false) Integer months) {
        userService.updateMemberLevel(userId, level, months);
        return ApiResult.success();
    }

    @Operation(summary = "检查用户名是否可用")
    @GetMapping("/check-username")
    public ApiResult<Boolean> checkUsername(
            @Parameter(description = "用户名") @RequestParam String username) {
        return ApiResult.success(userService.checkUsername(username));
    }

    @Operation(summary = "检查手机号是否可用")
    @GetMapping("/check-phone")
    public ApiResult<Boolean> checkPhone(
            @Parameter(description = "手机号") @RequestParam String phone) {
        return ApiResult.success(userService.checkPhone(phone));
    }

    @Operation(summary = "检查邮箱是否可用")
    @GetMapping("/check-email")
    public ApiResult<Boolean> checkEmail(
            @Parameter(description = "邮箱") @RequestParam String email) {
        return ApiResult.success(userService.checkEmail(email));
    }

    @Operation(summary = "发送验证码")
    @PostMapping("/send-verification-code")
    public ApiResult<String> sendVerificationCode(
            @Parameter(description = "手机号") @RequestParam String phone) {
        return ApiResult.success(userService.sendVerificationCode(phone));
    }

    @Operation(summary = "验证验证码")
    @PostMapping("/verify-code")
    public ApiResult<Boolean> verifyCode(
            @Parameter(description = "手机号") @RequestParam String phone,
            @Parameter(description = "验证码") @RequestParam String code) {
        return ApiResult.success(userService.verifyCode(phone, code));
    }

    @Operation(summary = "查询用户列表")
    @GetMapping
    public ApiResult<Page<User>> list(
            @Parameter(description = "用户名") @RequestParam(required = false) String username,
            @Parameter(description = "手机号") @RequestParam(required = false) String phone,
            @Parameter(description = "邮箱") @RequestParam(required = false) String email,
            @Parameter(description = "会员等级") @RequestParam(required = false) Integer memberLevel,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") Integer pageSize) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(username != null, User::getUsername, username)
                .like(phone != null, User::getPhone, phone)
                .like(email != null, User::getEmail, email)
                .eq(memberLevel != null, User::getMemberLevel, memberLevel)
                .eq(status != null, User::getStatus, status)
                .orderByDesc(User::getCreateTime);
        return ApiResult.success(userService.page(new Page<>(pageNum, pageSize), wrapper));
    }

    @Operation(summary = "查询用户详情")
    @GetMapping("/{userId}")
    public ApiResult<User> getById(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        return ApiResult.success(userService.getById(userId));
    }
}