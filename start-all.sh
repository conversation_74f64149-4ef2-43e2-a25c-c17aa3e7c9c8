#!/bin/bash

echo "========================================"
echo "体育场馆管理系统 - 毕业设计版本"
echo "========================================"
echo

echo "正在检查环境..."

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "[错误] 未检测到Java环境，请安装JDK 11或更高版本"
    exit 1
fi

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "[错误] 未检测到Node.js环境，请安装Node.js 14或更高版本"
    exit 1
fi

# 检查MySQL服务
if ! pgrep -x "mysqld" > /dev/null; then
    echo "[警告] MySQL服务未启动，请确保MySQL服务正在运行"
fi

echo "[信息] 环境检查完成"
echo

# 启动后端服务
echo "正在启动后端服务..."
cd backend
gnome-terminal --title="后端服务" -- bash -c "mvn spring-boot:run; exec bash" 2>/dev/null || \
xterm -title "后端服务" -e "mvn spring-boot:run; bash" 2>/dev/null || \
osascript -e 'tell app "Terminal" to do script "cd '$(pwd)' && mvn spring-boot:run"' 2>/dev/null || \
(mvn spring-boot:run &)
cd ..

# 等待后端启动
echo "等待后端服务启动..."
sleep 10

# 启动前端服务
echo "正在启动前端服务..."
cd frontend
gnome-terminal --title="前端服务" -- bash -c "npm run serve; exec bash" 2>/dev/null || \
xterm -title "前端服务" -e "npm run serve; bash" 2>/dev/null || \
osascript -e 'tell app "Terminal" to do script "cd '$(pwd)' && npm run serve"' 2>/dev/null || \
(npm run serve &)
cd ..

echo
echo "========================================"
echo "启动完成！"
echo "前端地址: http://localhost:8083"
echo "后端地址: http://localhost:8080/api"
echo "========================================"
echo
echo "默认账号信息："
echo "系统管理员: admin / 123456"
echo "场馆管理员: venue / 123456"
echo "财务人员: finance / 123456"
echo "普通用户: user / 123456"
echo
echo "按Ctrl+C退出..."

# 保持脚本运行
while true; do
    sleep 1
done