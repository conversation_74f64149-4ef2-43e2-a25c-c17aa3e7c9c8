@echo off
chcp 65001
echo 开始修复javax.validation到jakarta.validation的迁移...

echo 正在替换 javax.validation 为 jakarta.validation...
powershell -Command "(Get-ChildItem -Path 'backend\src\main\java' -Filter '*.java' -Recurse) | ForEach-Object { (Get-Content $_.FullName) -replace 'javax\.validation', 'jakarta.validation' | Set-Content $_.FullName }"

echo 正在替换 javax.annotation 为 jakarta.annotation...
powershell -Command "(Get-ChildItem -Path 'backend\src\main\java' -Filter '*.java' -Recurse) | ForEach-Object { (Get-Content $_.FullName) -replace 'javax\.annotation', 'jakarta.annotation' | Set-Content $_.FullName }"

echo javax.validation 到 jakarta.validation 迁移完成！

echo 显示已修改的文件：
powershell -Command "Get-ChildItem -Path 'backend\src\main\java' -Filter '*.java' -Recurse | Where-Object { (Get-Content $_.FullName) -match 'jakarta\.validation' } | Select-Object FullName"

pause
