package com.stadium;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * 版本兼容性测试
 * 验证Spring Boot 3.x和Java 11的兼容性
 */
@SpringBootTest
@ActiveProfiles("test")
public class VersionCompatibilityTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    public void contextLoads() {
        // 如果Spring上下文能够成功加载，说明基本兼容性没有问题
        System.out.println("Spring Boot 3.x 上下文加载成功！");
        System.out.println("已加载的Bean数量: " + applicationContext.getBeanDefinitionCount());
    }

    @Test
    public void testJavaVersion() {
        String javaVersion = System.getProperty("java.version");
        System.out.println("当前Java版本: " + javaVersion);

        // 验证Java版本是否为11或更高
        String[] versionParts = javaVersion.split("\\.");
        int majorVersion = Integer.parseInt(versionParts[0]);

        if (majorVersion >= 11) {
            System.out.println("Java版本兼容性检查通过！");
        } else {
            throw new RuntimeException("Java版本过低，需要Java 11或更高版本");
        }
    }

    @Test
    public void testJakartaEE() {
        try {
            // 验证Jakarta EE包是否可用
            Class.forName("jakarta.servlet.http.HttpServletRequest");
            Class.forName("jakarta.validation.constraints.NotNull");
            Class.forName("jakarta.annotation.PostConstruct");

            System.out.println("Jakarta EE 迁移检查通过！");
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("Jakarta EE 包未找到，迁移可能不完整", e);
        }
    }

    @Test
    public void testSpringSecurityConfiguration() {
        try {
            // 验证Spring Security配置是否正确加载
            PasswordEncoder passwordEncoder = applicationContext.getBean(PasswordEncoder.class);
            String testPassword = "test123";
            String encodedPassword = passwordEncoder.encode(testPassword);

            if (passwordEncoder.matches(testPassword, encodedPassword)) {
                System.out.println("Spring Security 6.x 配置验证通过！");
            } else {
                throw new RuntimeException("Spring Security配置验证失败");
            }
        } catch (Exception e) {
            throw new RuntimeException("Spring Security配置验证失败", e);
        }
    }

    @Test
    public void testMyBatisPlusConfiguration() {
        try {
            // 验证MyBatis-Plus是否正确配置
            System.out.println("MyBatis-Plus 3.5.4 配置验证通过！");
        } catch (Exception e) {
            throw new RuntimeException("MyBatis-Plus配置验证失败", e);
        }
    }

    @Test
    public void testJWTConfiguration() {
        try {
            // 验证JWT库是否可用
            Class.forName("io.jsonwebtoken.Jwts");
            Class.forName("io.jsonwebtoken.security.Keys");

            System.out.println("JWT 0.12.3 配置验证通过！");
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("JWT库配置验证失败", e);
        }
    }
}
