package com.stadium.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 性能监控配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "performance")
public class PerformanceProperties {

    /**
     * 监控配置
     */
    private Monitor monitor = new Monitor();

    @Data
    public static class Monitor {
        /**
         * 是否启用性能监控
         */
        private Boolean enabled = true;

        /**
         * 是否启用数据库监控
         */
        private Boolean dbEnabled = true;

        /**
         * 是否启用方法监控
         */
        private Boolean methodEnabled = true;

        /**
         * 是否启用缓存监控
         */
        private Boolean cacheEnabled = true;

        /**
         * 是否启用JVM监控
         */
        private Boolean jvmEnabled = true;
    }
}
