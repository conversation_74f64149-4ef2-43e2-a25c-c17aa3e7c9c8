package com.stadium.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Future;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 活动数据传输对象
 */
@Data
@Schema(description = "活动信息")
public class ActivityDTO {

    @Schema(description = "活动ID")
    private Long id;

    @Schema(description = "活动名称")
    @NotBlank(message = "活动名称不能为空")
    private String name;

    @Schema(description = "活动描述")
    private String description;

    @Schema(description = "活动地点")
    @NotBlank(message = "活动地点不能为空")
    private String location;

    @Schema(description = "开始时间")
    @NotNull(message = "开始时间不能为空")
    @Future(message = "开始时间必须是将来时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @NotNull(message = "结束时间不能为空")
    @Future(message = "结束时间必须是将来时间")
    private LocalDateTime endTime;

    @Schema(description = "最大参与人数")
    @NotNull(message = "最大参与人数不能为空")
    @Positive(message = "最大参与人数必须大于0")
    private Integer maxParticipants;

    @Schema(description = "活动费用")
    @NotNull(message = "活动费用不能为空")
    @Positive(message = "活动费用必须大于0")
    private BigDecimal fee;

    @Schema(description = "活动图片")
    private String image;

    @Schema(description = "活动类型ID")
    private Long typeId;

    @Schema(description = "活动类型名称")
    private String typeName;

    @Schema(description = "活动状态：0-未开始，1-进行中，2-已结束，3-已取消")
    private Integer status;

    @Schema(description = "活动状态名称")
    private String statusName;

    @Schema(description = "当前参与人数")
    private Integer currentParticipants;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}