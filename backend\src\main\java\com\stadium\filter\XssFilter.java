package com.stadium.filter;

import com.stadium.util.HtmlUtils;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * XSS过滤器，用于过滤请求参数中的XSS攻击
 */
@Slf4j
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
@RequiredArgsConstructor
public class XssFilter implements Filter {

    private final HtmlUtils htmlUtils;

    // 排除的URL列表
    private static final List<String> EXCLUDE_URLS = Collections.unmodifiableList(Arrays.asList(
            "/api/swagger-ui",
            "/api/v3/api-docs",
            "/api/webjars",
            "/api/druid",
            "/api/actuator"));

    // 排除的参数名列表
    private static final List<String> EXCLUDE_PARAMS = Collections.unmodifiableList(Arrays.asList(
            "password",
            "token",
            "accessToken",
            "refreshToken",
            "captcha"));

    // 富文本内容参数名列表
    private static final List<String> RICH_TEXT_PARAMS = Collections.unmodifiableList(Arrays.asList(
            "content",
            "description",
            "richText",
            "html"));

    // 匹配URL的正则表达式
    private static final Pattern URL_PATTERN = Pattern.compile("^(https?|ftp)://[^\\s/$.?#].[^\\s]*$");

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("XSS过滤器初始化");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        String requestURI = httpRequest.getRequestURI();

        // 检查是否需要排除
        if (isExcludeUrl(requestURI)) {
            chain.doFilter(request, response);
            return;
        }

        // 包装请求
        XssHttpServletRequestWrapper xssRequest = new XssHttpServletRequestWrapper(httpRequest, htmlUtils);
        chain.doFilter(xssRequest, response);
    }

    @Override
    public void destroy() {
        log.info("XSS过滤器销毁");
    }

    /**
     * 检查是否为排除的URL
     *
     * @param url 请求URL
     * @return 是否排除
     */
    private boolean isExcludeUrl(String url) {
        if (url == null) {
            return false;
        }

        return EXCLUDE_URLS.stream().anyMatch(url::startsWith);
    }

    /**
     * XSS请求包装器
     */
    private static class XssHttpServletRequestWrapper extends HttpServletRequestWrapper {

        private final HtmlUtils htmlUtils;

        public XssHttpServletRequestWrapper(HttpServletRequest request, HtmlUtils htmlUtils) {
            super(request);
            this.htmlUtils = htmlUtils;
        }

        @Override
        public String getParameter(String name) {
            String value = super.getParameter(name);
            return cleanParameter(name, value);
        }

        @Override
        public String[] getParameterValues(String name) {
            String[] values = super.getParameterValues(name);
            if (values == null) {
                return null;
            }

            String[] cleanValues = new String[values.length];
            for (int i = 0; i < values.length; i++) {
                cleanValues[i] = cleanParameter(name, values[i]);
            }

            return cleanValues;
        }

        @Override
        public Map<String, String[]> getParameterMap() {
            Map<String, String[]> parameterMap = super.getParameterMap();
            if (parameterMap == null) {
                return null;
            }

            Map<String, String[]> cleanParameterMap = parameterMap.entrySet().stream()
                    .collect(java.util.stream.Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> {
                                String[] values = entry.getValue();
                                String[] cleanValues = new String[values.length];
                                for (int i = 0; i < values.length; i++) {
                                    cleanValues[i] = cleanParameter(entry.getKey(), values[i]);
                                }
                                return cleanValues;
                            }));

            return cleanParameterMap;
        }

        @Override
        public String getHeader(String name) {
            String value = super.getHeader(name);
            return cleanParameter(name, value);
        }

        /**
         * 清理参数值
         *
         * @param name  参数名
         * @param value 参数值
         * @return 清理后的参数值
         */
        private String cleanParameter(String name, String value) {
            if (value == null) {
                return null;
            }

            // 排除的参数不处理
            if (EXCLUDE_PARAMS.contains(name)) {
                return value;
            }

            // 富文本内容使用特殊处理
            if (RICH_TEXT_PARAMS.contains(name)) {
                return htmlUtils.sanitizeHtml(value);
            }

            // URL参数使用特殊处理
            if (name.toLowerCase().contains("url") && URL_PATTERN.matcher(value).matches()) {
                return htmlUtils.sanitizeUrl(value);
            }

            // CSS参数使用特殊处理
            if (name.toLowerCase().contains("style") || name.toLowerCase().contains("css")) {
                return htmlUtils.sanitizeCss(value);
            }

            // 其他参数进行HTML转义
            return htmlUtils.escapeHtml(value);
        }
    }
}
