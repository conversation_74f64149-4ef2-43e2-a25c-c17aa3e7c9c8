package com.stadium.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 短信服务配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "sms")
public class SmsConfig {

    /**
     * 是否启用短信服务（系统内部化，禁用外部短信服务）
     */
    private boolean enabled = false;

    /**
     * 是否使用模拟短信服务（系统内部化，仅支持站内消息）
     */
    private boolean mock = true;

    /**
     * 短信服务提供商，可选值：mock, aliyun, tencent
     */
    private String provider = "tencent";

    /**
     * 阿里云短信配置
     */
    private AliyunConfig aliyun = new AliyunConfig();

    /**
     * 腾讯云短信配置
     */
    private TencentConfig tencent = new TencentConfig();

    /**
     * 阿里云短信配置
     */
    @Data
    public static class AliyunConfig {
        /**
         * 访问密钥ID
         */
        private String accessKeyId;

        /**
         * 访问密钥密码
         */
        private String accessKeySecret;

        /**
         * 短信签名
         */
        private String signName;

        /**
         * 短信模板代码
         */
        private String templateCode;
    }

    /**
     * 腾讯云短信配置
     */
    @Data
    public static class TencentConfig {
        /**
         * 应用ID
         */
        private String appId;

        /**
         * 应用密钥
         */
        private String appKey;

        /**
         * 短信签名
         */
        private String signName;

        /**
         * 短信模板ID
         */
        private String templateId;
    }
}
