-- 体育场馆管理系统数据库初始化脚本（修复版）
-- 修复日期：2024-12-19
-- 修复内容：表名冲突、角色定义、索引优化、外键约束等

-- 创建数据库
CREATE DATABASE IF NOT EXISTS stadium_management DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE stadium_management;

-- 删除可能存在的旧表（按依赖关系顺序）
DROP TABLE IF EXISTS `activity_participant`;
DROP TABLE IF EXISTS `activity_check_in`;
DROP TABLE IF EXISTS `activity_registration`;
DROP TABLE IF EXISTS `activity_review`;
DROP TABLE IF EXISTS `activity`;
DROP TABLE IF EXISTS `venue_review`;
DROP TABLE IF EXISTS `venue_booking`;
DROP TABLE IF EXISTS `venue_maintenance`;
DROP TABLE IF EXISTS `venue_facility`;
DROP TABLE IF EXISTS `booking`;
DROP TABLE IF EXISTS `payment`;
DROP TABLE IF EXISTS `review`;
DROP TABLE IF EXISTS `user_role`;
DROP TABLE IF EXISTS `role_permission`;
DROP TABLE IF EXISTS `user_balance_log`;
DROP TABLE IF EXISTS `user_points_log`;
DROP TABLE IF EXISTS `member`;
DROP TABLE IF EXISTS `venue`;
DROP TABLE IF EXISTS `permission`;
DROP TABLE IF EXISTS `role`;
DROP TABLE IF EXISTS `users`; -- 删除冗余表
DROP TABLE IF EXISTS `user`;  -- 删除有问题的表
DROP TABLE IF EXISTS `sys_user`;

-- 用户表（修复表名冲突问题）
CREATE TABLE IF NOT EXISTS `sys_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `gender` tinyint(4) DEFAULT '0' COMMENT '性别：0-未知，1-男，2-女',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：0-禁用，1-正常',
  `role` varchar(20) DEFAULT 'USER' COMMENT '角色：ADMIN-系统管理员，VENUE_ADMIN-场馆管理员，FINANCE-财务人员，USER-普通用户',
  `balance` decimal(10,2) DEFAULT '0.00' COMMENT '余额',
  `points` int(11) DEFAULT '0' COMMENT '积分',
  `member_level` int(11) DEFAULT '0' COMMENT '会员等级：0-普通用户，1-VIP会员，2-SVIP会员',
  `member_expire_time` datetime DEFAULT NULL COMMENT '会员到期时间',
  `email_verified` tinyint(1) DEFAULT '0' COMMENT '邮箱是否已验证：0-未验证，1-已验证',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `lock_time` datetime DEFAULT NULL COMMENT '账号锁定时间',
  `lock_expire_time` datetime DEFAULT NULL COMMENT '账号锁定过期时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_email` (`email`),
  KEY `idx_phone` (`phone`),
  KEY `idx_status_role` (`status`, `role`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 角色表（修复角色定义不完整问题）
CREATE TABLE IF NOT EXISTS `role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `code` varchar(50) NOT NULL COMMENT '角色编码',
  `description` varchar(255) DEFAULT NULL COMMENT '角色描述',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 权限表
CREATE TABLE IF NOT EXISTS `permission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `name` varchar(50) NOT NULL COMMENT '权限名称',
  `code` varchar(50) NOT NULL COMMENT '权限编码',
  `type` tinyint(1) DEFAULT '1' COMMENT '类型：1-菜单，2-按钮',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父级ID',
  `path` varchar(255) DEFAULT NULL COMMENT '路径',
  `component` varchar(255) DEFAULT NULL COMMENT '组件',
  `icon` varchar(50) DEFAULT NULL COMMENT '图标',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- 用户角色关联表（修复外键约束）
CREATE TABLE IF NOT EXISTS `user_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_role` (`user_id`, `role_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_id` (`role_id`),
  CONSTRAINT `fk_ur_user_id` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_ur_role_id` FOREIGN KEY (`role_id`) REFERENCES `role` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS `role_permission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `permission_id` bigint(20) NOT NULL COMMENT '权限ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_permission_id` (`permission_id`),
  CONSTRAINT `fk_rp_role_id` FOREIGN KEY (`role_id`) REFERENCES `role` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_rp_permission_id` FOREIGN KEY (`permission_id`) REFERENCES `permission` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- 场馆表（优化索引）
CREATE TABLE IF NOT EXISTS `venue` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '场馆ID',
  `name` varchar(100) NOT NULL COMMENT '场馆名称',
  `type` tinyint(4) NOT NULL COMMENT '场馆类型：1-篮球场，2-足球场，3-网球场，4-羽毛球场，5-乒乓球场，6-游泳池，7-健身房，8-其他',
  `capacity` int(11) NOT NULL COMMENT '容量',
  `area` decimal(10,2) NOT NULL COMMENT '面积（平方米）',
  `price` decimal(10,2) NOT NULL COMMENT '价格（元/小时）',
  `description` text COMMENT '描述',
  `image` varchar(255) COMMENT '图片URL',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `business_start_time` time NOT NULL DEFAULT '08:00:00' COMMENT '营业开始时间',
  `business_end_time` time NOT NULL DEFAULT '22:00:00' COMMENT '营业结束时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_status_type` (`status`, `type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='场馆表';

-- 预约表（修复外键约束和索引）
CREATE TABLE IF NOT EXISTS `booking` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '预约ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `venue_id` bigint(20) NOT NULL COMMENT '场馆ID',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1-待支付，2-已支付，3-已完成，4-已取消',
  `amount` decimal(10,2) NOT NULL COMMENT '金额',
  `payment_method` varchar(20) DEFAULT 'ONLINE' COMMENT '支付方式',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_venue_id` (`venue_id`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_status_user` (`status`, `user_id`),
  KEY `idx_start_end_time` (`start_time`, `end_time`),
  CONSTRAINT `fk_booking_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_booking_venue` FOREIGN KEY (`venue_id`) REFERENCES `venue` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='预约表';

-- 活动表（修复外键约束和索引）
CREATE TABLE IF NOT EXISTS `activity` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `name` varchar(100) NOT NULL COMMENT '活动名称',
  `venue_id` bigint(20) NOT NULL COMMENT '场馆ID',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `max_participants` int(11) NOT NULL COMMENT '最大参与人数',
  `current_participants` int(11) NOT NULL DEFAULT '0' COMMENT '当前参与人数',
  `description` text COMMENT '描述',
  `image` varchar(255) COMMENT '图片URL',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-未开始，1-进行中，2-已结束，3-已取消',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_venue_id` (`venue_id`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_status_start_time` (`status`, `start_time`),
  CONSTRAINT `fk_activity_venue` FOREIGN KEY (`venue_id`) REFERENCES `venue` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='活动表';

-- 活动参与表
CREATE TABLE IF NOT EXISTS `activity_participant` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '参与ID',
  `activity_id` bigint(20) NOT NULL COMMENT '活动ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1-已报名，2-已签到，3-已取消',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_activity_user` (`activity_id`,`user_id`),
  KEY `idx_activity_id` (`activity_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_activity_participant_activity` FOREIGN KEY (`activity_id`) REFERENCES `activity` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_activity_participant_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='活动参与表';

-- 支付记录表（修复外键约束）
CREATE TABLE IF NOT EXISTS `payment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '支付ID',
  `booking_id` bigint(20) NOT NULL COMMENT '预约ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `payment_method` varchar(20) NOT NULL COMMENT '支付方式',
  `payment_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '支付状态：1-待支付，2-支付成功，3-支付失败',
  `transaction_id` varchar(100) DEFAULT NULL COMMENT '交易流水号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_booking_id` (`booking_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_payment_status` (`payment_status`),
  CONSTRAINT `fk_payment_booking` FOREIGN KEY (`booking_id`) REFERENCES `booking` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_payment_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付记录表';

-- 评价表（修复外键约束）
CREATE TABLE IF NOT EXISTS `review` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `booking_id` bigint(20) NOT NULL COMMENT '预约ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `venue_id` bigint(20) NOT NULL COMMENT '场馆ID',
  `rating` tinyint(4) NOT NULL COMMENT '评分：1-5分',
  `content` text COMMENT '评价内容',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_booking_id` (`booking_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_venue_id` (`venue_id`),
  CONSTRAINT `fk_review_booking` FOREIGN KEY (`booking_id`) REFERENCES `booking` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_review_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_review_venue` FOREIGN KEY (`venue_id`) REFERENCES `venue` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评价表';

-- 用户余额变动记录表
CREATE TABLE IF NOT EXISTS `user_balance_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `type` tinyint(4) NOT NULL COMMENT '变动类型：1-充值，2-消费，3-退款，4-系统赠送',
  `amount` decimal(10,2) NOT NULL COMMENT '变动金额',
  `balance` decimal(10,2) NOT NULL COMMENT '变动后余额',
  `order_no` varchar(50) DEFAULT NULL COMMENT '关联订单号',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_balance_log_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户余额变动记录表';

-- 用户积分变动记录表
CREATE TABLE IF NOT EXISTS `user_points_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `type` tinyint(4) NOT NULL COMMENT '变动类型：1-消费，2-退款，3-活动奖励，4-系统赠送',
  `points` int(11) NOT NULL COMMENT '变动积分',
  `balance` int(11) NOT NULL COMMENT '变动后积分',
  `order_no` varchar(50) DEFAULT NULL COMMENT '关联订单号',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_points_log_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户积分变动记录表';

-- 初始化角色数据（修复角色定义不完整问题）
INSERT INTO `role` (`id`, `name`, `code`, `description`, `status`, `sort`) VALUES
(1, '系统管理员', 'ADMIN', '系统最高权限，可以管理所有功能', 1, 1),
(2, '场馆管理员', 'VENUE_ADMIN', '负责场馆管理、预约审核、活动组织等功能', 1, 2),
(3, '财务人员', 'FINANCE', '负责财务管理、支付审核、退款处理等功能', 1, 3),
(4, '普通用户', 'USER', '可以预约场馆、参加活动、查看个人信息等', 1, 4);

-- 初始化权限数据
INSERT INTO `permission` (`id`, `name`, `code`, `type`, `parent_id`, `path`, `component`, `icon`, `sort`, `status`) VALUES
(1, '系统管理', 'system', 1, NULL, '/system', 'Layout', 'setting', 1, 1),
(2, '用户管理', 'system:user', 1, 1, 'user', 'system/user/index', 'user', 1, 1),
(3, '角色管理', 'system:role', 1, 1, 'role', 'system/role/index', 'peoples', 2, 1),
(4, '权限管理', 'system:permission', 1, 1, 'permission', 'system/permission/index', 'tree-table', 3, 1),
(5, '场馆管理', 'venue', 1, NULL, '/venue', 'Layout', 'building', 2, 1),
(6, '场馆列表', 'venue:list', 1, 5, 'list', 'venue/list/index', 'list', 1, 1),
(7, '预约管理', 'booking', 1, NULL, '/booking', 'Layout', 'date', 3, 1),
(8, '预约列表', 'booking:list', 1, 7, 'list', 'booking/list/index', 'list', 1, 1),
(9, '活动管理', 'activity', 1, NULL, '/activity', 'Layout', 'star', 4, 1),
(10, '活动列表', 'activity:list', 1, 9, 'list', 'activity/list/index', 'list', 1, 1),
(11, '财务管理', 'finance', 1, NULL, '/finance', 'Layout', 'money', 5, 1),
(12, '支付管理', 'finance:payment', 1, 11, 'payment', 'finance/payment/index', 'money', 1, 1);

-- 初始化角色权限关联（管理员拥有所有权限）
INSERT INTO `role_permission` (`role_id`, `permission_id`) VALUES
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5), (1, 6), (1, 7), (1, 8), (1, 9), (1, 10), (1, 11), (1, 12),
(2, 5), (2, 6), (2, 7), (2, 8), (2, 9), (2, 10),
(3, 7), (3, 8), (3, 11), (3, 12),
(4, 5), (4, 6), (4, 7), (4, 8), (4, 9), (4, 10);

-- 初始化用户数据（密码均为123456的BCrypt加密）
INSERT INTO `sys_user` (`id`, `username`, `nickname`, `password`, `real_name`, `role`, `status`) VALUES
(1, 'admin', '系统管理员', '$2a$10$ySG2lkvjFHY5O0./CPIE1OI8VJsuKYEzOYzqIa7AJR6sEgSzUFOAm', '管理员', 'ADMIN', 1),
(2, 'venue', '场馆管理员', '$2a$10$ySG2lkvjFHY5O0./CPIE1OI8VJsuKYEzOYzqIa7AJR6sEgSzUFOAm', '场馆管理员', 'VENUE_ADMIN', 1),
(3, 'finance', '财务人员', '$2a$10$ySG2lkvjFHY5O0./CPIE1OI8VJsuKYEzOYzqIa7AJR6sEgSzUFOAm', '财务人员', 'FINANCE', 1),
(4, 'user', '普通用户', '$2a$10$ySG2lkvjFHY5O0./CPIE1OI8VJsuKYEzOYzqIa7AJR6sEgSzUFOAm', '普通用户', 'USER', 1);

-- 关联用户和角色
INSERT INTO `user_role` (`user_id`, `role_id`) VALUES
(1, 1), -- admin -> ADMIN
(2, 2), -- venue -> VENUE_ADMIN
(3, 3), -- finance -> FINANCE
(4, 4); -- user -> USER

-- 初始化场馆数据
INSERT INTO `venue` (`name`, `type`, `capacity`, `area`, `price`, `description`, `status`) VALUES
('篮球场A', 1, 20, 420.00, 80.00, '标准篮球场，设施齐全', 1),
('足球场B', 2, 22, 7140.00, 200.00, '标准足球场，草坪维护良好', 1),
('网球场C', 3, 4, 260.00, 60.00, '标准网球场，适合双打', 1),
('羽毛球场D', 4, 4, 81.00, 40.00, '标准羽毛球场，灯光充足', 1),
('游泳池E', 6, 50, 500.00, 100.00, '标准游泳池，水质清洁', 1);

-- 初始化活动数据
INSERT INTO `activity` (`name`, `venue_id`, `start_time`, `end_time`, `max_participants`, `description`, `status`) VALUES
('篮球友谊赛', 1, '2024-12-25 14:00:00', '2024-12-25 16:00:00', 20, '欢迎篮球爱好者参加', 0),
('足球训练营', 2, '2024-12-26 09:00:00', '2024-12-26 11:00:00', 22, '专业教练指导', 0),
('网球体验课', 3, '2024-12-27 15:00:00', '2024-12-27 17:00:00', 4, '适合初学者', 0);

-- 用户钱包表（站内支付系统）
CREATE TABLE IF NOT EXISTS `user_wallet` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '钱包ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `balance` DECIMAL(10,2) DEFAULT 0.00 COMMENT '余额',
    `frozen_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '冻结金额',
    `total_recharge` DECIMAL(10,2) DEFAULT 0.00 COMMENT '总充值金额',
    `total_consume` DECIMAL(10,2) DEFAULT 0.00 COMMENT '总消费金额',
    `status` INT DEFAULT 0 COMMENT '钱包状态：0-正常，1-冻结',
    `pay_password` VARCHAR(255) COMMENT '支付密码',
    `last_transaction_time` DATETIME COMMENT '最后交易时间',
    `version` INT DEFAULT 1 COMMENT '版本号（乐观锁）',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT DEFAULT 0 COMMENT '是否删除：0-否，1-是',
    UNIQUE KEY `uk_user_id` (`user_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_status` (`status`),
    CONSTRAINT `fk_wallet_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户钱包表';

-- 钱包交易记录表
CREATE TABLE IF NOT EXISTS `wallet_transaction` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '交易ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `type` INT NOT NULL COMMENT '交易类型：1-充值，2-消费，3-退款，4-冻结，5-解冻',
    `amount` DECIMAL(10,2) NOT NULL COMMENT '交易金额',
    `balance_before` DECIMAL(10,2) NOT NULL COMMENT '交易前余额',
    `balance_after` DECIMAL(10,2) NOT NULL COMMENT '交易后余额',
    `description` VARCHAR(255) COMMENT '交易描述',
    `related_order_no` VARCHAR(64) COMMENT '关联订单号',
    `status` INT DEFAULT 1 COMMENT '交易状态：0-处理中，1-成功，2-失败',
    `transaction_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '交易时间',
    `operator_id` BIGINT COMMENT '操作员ID',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT DEFAULT 0 COMMENT '是否删除：0-否，1-是',
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_type` (`type`),
    INDEX `idx_status` (`status`),
    INDEX `idx_transaction_time` (`transaction_time`),
    INDEX `idx_related_order_no` (`related_order_no`),
    CONSTRAINT `fk_transaction_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='钱包交易记录表';

-- 系统消息表（站内消息系统）
CREATE TABLE IF NOT EXISTS `system_message` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '消息ID',
    `title` VARCHAR(255) NOT NULL COMMENT '消息标题',
    `content` TEXT NOT NULL COMMENT '消息内容',
    `type` INT NOT NULL COMMENT '消息类型：1-系统通知，2-预订提醒，3-活动通知，4-支付通知，5-维护通知',
    `send_type` INT DEFAULT 1 COMMENT '发送方式：1-站内消息，2-弹窗提醒',
    `receiver_type` INT NOT NULL COMMENT '接收者类型：1-全体用户，2-指定用户，3-指定角色',
    `receiver_ids` TEXT COMMENT '接收者ID列表',
    `sender_id` BIGINT COMMENT '发送者ID',
    `sender_name` VARCHAR(100) COMMENT '发送者姓名',
    `status` INT DEFAULT 0 COMMENT '消息状态：0-草稿，1-已发送，2-已撤回',
    `is_top` BOOLEAN DEFAULT FALSE COMMENT '是否置顶',
    `is_urgent` BOOLEAN DEFAULT FALSE COMMENT '是否紧急',
    `scheduled_time` DATETIME COMMENT '计划发送时间',
    `sent_time` DATETIME COMMENT '实际发送时间',
    `expire_time` DATETIME COMMENT '过期时间',
    `attachment_url` VARCHAR(500) COMMENT '附件URL',
    `business_id` VARCHAR(64) COMMENT '关联业务ID',
    `business_type` VARCHAR(50) COMMENT '关联业务类型',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT DEFAULT 0 COMMENT '是否删除：0-否，1-是',
    INDEX `idx_type` (`type`),
    INDEX `idx_receiver_type` (`receiver_type`),
    INDEX `idx_status` (`status`),
    INDEX `idx_sent_time` (`sent_time`),
    INDEX `idx_business` (`business_type`, `business_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统消息表';

-- 用户消息表
CREATE TABLE IF NOT EXISTS `user_message` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户消息ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `message_id` BIGINT COMMENT '系统消息ID',
    `title` VARCHAR(255) NOT NULL COMMENT '消息标题',
    `content` TEXT NOT NULL COMMENT '消息内容',
    `type` INT NOT NULL COMMENT '消息类型：1-系统通知，2-预订提醒，3-活动通知，4-支付通知，5-维护通知',
    `sender_id` BIGINT COMMENT '发送者ID',
    `sender_name` VARCHAR(100) COMMENT '发送者姓名',
    `is_read` INT DEFAULT 0 COMMENT '是否已读：0-未读，1-已读',
    `is_top` BOOLEAN DEFAULT FALSE COMMENT '是否置顶',
    `is_urgent` BOOLEAN DEFAULT FALSE COMMENT '是否紧急',
    `is_favorite` BOOLEAN DEFAULT FALSE COMMENT '是否收藏',
    `read_time` DATETIME COMMENT '读取时间',
    `sent_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
    `expire_time` DATETIME COMMENT '过期时间',
    `attachment_url` VARCHAR(500) COMMENT '附件URL',
    `business_id` VARCHAR(64) COMMENT '关联业务ID',
    `business_type` VARCHAR(50) COMMENT '关联业务类型',
    `action_buttons` JSON COMMENT '操作按钮配置',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT DEFAULT 0 COMMENT '是否删除：0-否，1-是',
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_message_id` (`message_id`),
    INDEX `idx_type` (`type`),
    INDEX `idx_is_read` (`is_read`),
    INDEX `idx_sent_time` (`sent_time`),
    INDEX `idx_business` (`business_type`, `business_id`),
    CONSTRAINT `fk_user_message_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_user_message_system` FOREIGN KEY (`message_id`) REFERENCES `system_message` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户消息表';

-- 初始化钱包数据（为现有用户创建钱包）
INSERT INTO `user_wallet` (`user_id`, `balance`, `frozen_amount`, `total_recharge`, `total_consume`, `status`, `version`) VALUES
(1, 1000.00, 0.00, 1000.00, 0.00, 0, 1),  -- admin 初始余额1000元
(2, 500.00, 0.00, 500.00, 0.00, 0, 1),    -- venue 初始余额500元
(3, 300.00, 0.00, 300.00, 0.00, 0, 1),    -- finance 初始余额300元
(4, 100.00, 0.00, 100.00, 0.00, 0, 1);    -- user 初始余额100元

-- 初始化钱包交易记录（初始充值记录）
INSERT INTO `wallet_transaction` (`user_id`, `type`, `amount`, `balance_before`, `balance_after`, `description`, `status`, `transaction_time`) VALUES
(1, 1, 1000.00, 0.00, 1000.00, '系统初始化充值', 1, NOW()),
(2, 1, 500.00, 0.00, 500.00, '系统初始化充值', 1, NOW()),
(3, 1, 300.00, 0.00, 300.00, '系统初始化充值', 1, NOW()),
(4, 1, 100.00, 0.00, 100.00, '系统初始化充值', 1, NOW());

-- 初始化系统消息（欢迎消息）
INSERT INTO `system_message` (`title`, `content`, `type`, `receiver_type`, `sender_id`, `sender_name`, `status`, `sent_time`) VALUES
('欢迎使用体育场馆管理系统', '欢迎您使用体育场馆管理系统！系统提供完整的场馆预订、活动参与、站内支付等功能。如有任何问题，请联系管理员。', 1, 1, 1, '系统管理员', 1, NOW()),
('站内支付系统上线', '站内支付系统已正式上线！您可以通过钱包进行充值、消费、查看交易记录等操作。支持余额支付，安全便捷。', 4, 1, 1, '系统管理员', 1, NOW());

-- 为所有用户发送欢迎消息
INSERT INTO `user_message` (`user_id`, `message_id`, `title`, `content`, `type`, `sender_id`, `sender_name`, `is_read`, `sent_time`) VALUES
(1, 1, '欢迎使用体育场馆管理系统', '欢迎您使用体育场馆管理系统！系统提供完整的场馆预订、活动参与、站内支付等功能。如有任何问题，请联系管理员。', 1, 1, '系统管理员', 0, NOW()),
(2, 1, '欢迎使用体育场馆管理系统', '欢迎您使用体育场馆管理系统！系统提供完整的场馆预订、活动参与、站内支付等功能。如有任何问题，请联系管理员。', 1, 1, '系统管理员', 0, NOW()),
(3, 1, '欢迎使用体育场馆管理系统', '欢迎您使用体育场馆管理系统！系统提供完整的场馆预订、活动参与、站内支付等功能。如有任何问题，请联系管理员。', 1, 1, '系统管理员', 0, NOW()),
(4, 1, '欢迎使用体育场馆管理系统', '欢迎您使用体育场馆管理系统！系统提供完整的场馆预订、活动参与、站内支付等功能。如有任何问题，请联系管理员。', 1, 1, '系统管理员', 0, NOW()),
(1, 2, '站内支付系统上线', '站内支付系统已正式上线！您可以通过钱包进行充值、消费、查看交易记录等操作。支持余额支付，安全便捷。', 4, 1, '系统管理员', 0, NOW()),
(2, 2, '站内支付系统上线', '站内支付系统已正式上线！您可以通过钱包进行充值、消费、查看交易记录等操作。支持余额支付，安全便捷。', 4, 1, '系统管理员', 0, NOW()),
(3, 2, '站内支付系统上线', '站内支付系统已正式上线！您可以通过钱包进行充值、消费、查看交易记录等操作。支持余额支付，安全便捷。', 4, 1, '系统管理员', 0, NOW()),
(4, 2, '站内支付系统上线', '站内支付系统已正式上线！您可以通过钱包进行充值、消费、查看交易记录等操作。支持余额支付，安全便捷。', 4, 1, '系统管理员', 0, NOW());