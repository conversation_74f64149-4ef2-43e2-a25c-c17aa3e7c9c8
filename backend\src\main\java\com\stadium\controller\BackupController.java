package com.stadium.controller;

import com.stadium.common.api.ApiResult;
import com.stadium.service.BackupService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 数据备份控制器
 * 处理数据库备份和恢复操作
 */
@RestController
@RequestMapping("/api/backup")
@RequiredArgsConstructor
public class BackupController {

    private final BackupService backupService;

    /**
     * 备份数据库
     *
     * @param response HTTP响应对象，用于下载文件
     */
    @GetMapping
    @PreAuthorize("hasAuthority('backup:create')")
    public void backup(HttpServletResponse response) throws IOException {
        String fileName = "stadium_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"))
                + ".sql";

        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

        backupService.backup(response.getOutputStream());
    }

    /**
     * 恢复数据库
     *
     * @param file 备份文件
     * @return 操作结果
     */
    @PostMapping("/restore")
    @PreAuthorize("hasAuthority('backup:restore')")
    public ApiResult<Boolean> restore(@RequestParam MultipartFile file) throws IOException {
        return ApiResult.success(backupService.restore(file));
    }

    /**
     * 获取备份列表
     *
     * @return 备份文件列表
     */
    @GetMapping("/list")
    @PreAuthorize("hasAuthority('backup:list')")
    public ApiResult<Object> list() {
        return ApiResult.success(backupService.list());
    }

    /**
     * 删除备份文件
     *
     * @param fileName 文件名
     * @return 操作结果
     */
    @DeleteMapping("/{fileName}")
    @PreAuthorize("hasAuthority('backup:delete')")
    public ApiResult<Boolean> delete(@PathVariable String fileName) {
        return ApiResult.success(backupService.delete(fileName));
    }
}