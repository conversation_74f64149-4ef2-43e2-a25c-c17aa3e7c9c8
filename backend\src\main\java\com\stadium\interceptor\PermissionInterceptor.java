package com.stadium.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stadium.common.api.ApiResult;
import com.stadium.entity.Permission;
import com.stadium.entity.User;
import com.stadium.service.PermissionService;
import com.stadium.service.UserService;
import com.stadium.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;

import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 权限拦截器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PermissionInterceptor implements HandlerInterceptor {

    private final JwtUtil jwtUtil;
    private final UserService userService;
    private final PermissionService permissionService;
    private final ObjectMapper objectMapper;

    // 路径匹配器
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    // 权限缓存，用于缓存URL和权限的映射关系
    private final Map<String, String> permissionCache = new ConcurrentHashMap<>();

    // 白名单路径，不需要进行权限校验
    private final String[] whiteList = {
            "/api/auth/**",
            "/api/users/login",
            "/api/users/register",
            "/api/captcha/**",
            "/api/swagger-ui/**",
            "/api/v3/api-docs/**",
            "/api/webjars/**",
            "/api/druid/**",
            "/api/actuator/**"
    };

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
            @NonNull Object handler)
            throws Exception {
        // 获取请求路径
        String requestURI = request.getRequestURI();

        // 检查是否是白名单路径
        if (isWhiteListPath(requestURI)) {
            return true;
        }

        // 获取请求头中的token
        String token = request.getHeader("Authorization");
        if (!StringUtils.hasText(token)) {
            handleError(response, "未登录");
            return false;
        }

        // 验证token
        if (jwtUtil.isTokenBlacklisted(token)) {
            handleError(response, "token已过期");
            return false;
        }

        // 获取用户名
        String username = jwtUtil.getUsernameFromToken(token);
        if (username == null) {
            handleError(response, "用户不存在");
            return false;
        }

        // 检查用户是否存在
        User user = userService.getByUsername(username);
        if (user == null) {
            handleError(response, "用户不存在");
            return false;
        }

        // 检查用户状态
        if (user.getStatus() != 1) {
            handleError(response, "用户已被禁用");
            return false;
        }

        // 如果是超级管理员，直接放行
        if ("ADMIN".equals(user.getRole())) {
            return true;
        }

        // 检查是否有权限访问
        if (!checkPermission(user, requestURI, request.getMethod())) {
            handleError(response, "没有访问权限");
            return false;
        }

        return true;
    }

    /**
     * 检查是否是白名单路径
     *
     * @param requestURI 请求路径
     * @return 是否是白名单路径
     */
    private boolean isWhiteListPath(String requestURI) {
        for (String path : whiteList) {
            if (pathMatcher.match(path, requestURI)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查用户是否有权限访问
     *
     * @param user       用户
     * @param requestURI 请求路径
     * @param method     请求方法
     * @return 是否有权限访问
     */
    private boolean checkPermission(User user, String requestURI, String method) {
        // 构建权限缓存的key
        String cacheKey = requestURI + ":" + method;

        // 从缓存中获取权限编码
        String permissionCode = permissionCache.get(cacheKey);

        // 如果缓存中没有，则从数据库中查询
        if (permissionCode == null) {
            // 获取用户权限列表
            List<Permission> permissions = permissionService.getUserPermissions(user.getId());

            // 检查是否有匹配的权限
            for (Permission permission : permissions) {
                if (permission.getPath() != null && pathMatcher.match(permission.getPath(), requestURI)) {
                    // 将权限编码放入缓存
                    permissionCache.put(cacheKey, permission.getCode());
                    return true;
                }
            }

            // 如果没有匹配的权限，则放入一个空字符串，避免每次都查询数据库
            permissionCache.put(cacheKey, "");
            return false;
        }

        // 如果缓存中有权限编码，则直接判断
        return !permissionCode.isEmpty();
    }

    private void handleError(HttpServletResponse response, String message) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        ApiResult<Void> result = ApiResult.forbidden();
        result.setMessage(message);
        response.getWriter().write(objectMapper.writeValueAsString(result));
    }
}