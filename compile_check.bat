@echo off
chcp 65001
echo ========================================
echo 体育场馆管理系统编译检查
echo ========================================
echo.

cd backend

echo 正在检查Maven环境...
mvn --version
if %errorlevel% neq 0 (
    echo [错误] Maven环境未正确配置
    pause
    exit /b 1
)

echo.
echo 正在清理项目...
mvn clean -q
if %errorlevel% neq 0 (
    echo [错误] 项目清理失败
    pause
    exit /b 1
)

echo.
echo 正在编译项目（跳过测试）...
mvn compile -DskipTests
if %errorlevel% neq 0 (
    echo [错误] 项目编译失败
    echo 请检查编译错误信息
    pause
    exit /b 1
) else (
    echo [成功] 项目编译成功
)

echo.
echo 正在检查依赖解析...
mvn dependency:resolve -q
if %errorlevel% neq 0 (
    echo [警告] 依赖解析可能存在问题
) else (
    echo [成功] 依赖解析正常
)

echo.
echo ========================================
echo 编译检查完成
echo ========================================
echo.
echo 如果编译成功，说明代码修复工作已完成
echo 如果编译失败，请查看上方的错误信息

cd ..
pause
