package com.stadium.service;

import com.stadium.dto.FinancialReportDTO;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 财务报表服务接口
 */
public interface FinancialReportService {

    /**
     * 获取日收入报表
     *
     * @param date 日期
     * @return 财务报表
     */
    FinancialReportDTO getDailyReport(LocalDate date);

    /**
     * 获取月收入报表
     *
     * @param year  年份
     * @param month 月份
     * @return 财务报表
     */
    FinancialReportDTO getMonthlyReport(Integer year, Integer month);

    /**
     * 获取年收入报表
     *
     * @param year 年份
     * @return 财务报表
     */
    FinancialReportDTO getYearlyReport(Integer year);

    /**
     * 获取自定义时间段报表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 财务报表
     */
    FinancialReportDTO getCustomReport(LocalDate startDate, LocalDate endDate);

    /**
     * 导出日报表
     *
     * @param date     日期
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    void exportDailyReport(LocalDate date, HttpServletResponse response) throws IOException;

    /**
     * 导出月报表
     *
     * @param year     年份
     * @param month    月份
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    void exportMonthlyReport(Integer year, Integer month, HttpServletResponse response) throws IOException;

    /**
     * 导出年报表
     *
     * @param year     年份
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    void exportYearlyReport(Integer year, HttpServletResponse response) throws IOException;

    /**
     * 导出自定义时间段报表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param response  HTTP响应
     * @throws IOException IO异常
     */
    void exportCustomReport(LocalDate startDate, LocalDate endDate, HttpServletResponse response) throws IOException;

    /**
     * 获取按支付方式分类的收入统计
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 收入统计
     */
    List<Map<String, Object>> getIncomeByPaymentMethod(LocalDate startDate, LocalDate endDate);

    /**
     * 获取按场馆分类的收入统计
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 收入统计
     */
    List<Map<String, Object>> getIncomeByVenue(LocalDate startDate, LocalDate endDate);

    /**
     * 获取按日期分组的收入统计
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param groupBy   分组类型（day, week, month, year）
     * @return 收入统计
     */
    List<Map<String, Object>> getIncomeByDate(LocalDate startDate, LocalDate endDate, String groupBy);

    /**
     * 获取收入趋势
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 收入趋势
     */
    List<Map<String, Object>> getIncomeTrend(LocalDate startDate, LocalDate endDate);

    /**
     * 获取退款统计
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 退款统计
     */
    Map<String, Object> getRefundStatistics(LocalDate startDate, LocalDate endDate);

    /**
     * 生成对账单
     *
     * @param date 日期
     * @return 对账单文件路径
     */
    String generateReconciliation(LocalDate date);
}