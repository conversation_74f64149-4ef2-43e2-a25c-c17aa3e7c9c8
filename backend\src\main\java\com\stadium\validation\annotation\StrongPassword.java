package com.stadium.validation.annotation;

import com.stadium.validation.validator.StrongPasswordValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * 强密码验证注解
 */
@Documented
@Constraint(validatedBy = StrongPasswordValidator.class)
@Target({ ElementType.FIELD, ElementType.PARAMETER })
@Retention(RetentionPolicy.RUNTIME)
public @interface StrongPassword {

    String message() default "密码不符合安全要求";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    /**
     * 最小长度
     */
    int minLength() default 8;

    /**
     * 是否要求包含大写字母
     */
    boolean requireUppercase() default true;

    /**
     * 是否要求包含小写字母
     */
    boolean requireLowercase() default true;

    /**
     * 是否要求包含数字
     */
    boolean requireDigit() default true;

    /**
     * 是否要求包含特殊字符
     */
    boolean requireSpecialChar() default false;
}
