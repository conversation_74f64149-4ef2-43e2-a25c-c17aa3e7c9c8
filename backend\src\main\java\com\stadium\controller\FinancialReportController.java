package com.stadium.controller;

import com.stadium.common.api.ApiResult;
import com.stadium.dto.FinancialReportDTO;
import com.stadium.service.FinancialReportService;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 财务报表控制器
 */
@RestController
@RequestMapping("/api/financial-reports")
@RequiredArgsConstructor
public class FinancialReportController {

    private final FinancialReportService financialReportService;

    /**
     * 获取日收入报表
     */
    @GetMapping("/daily")
    @PreAuthorize("hasAuthority('report:view')")
    public ApiResult<FinancialReportDTO> getDailyReport(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {
        return ApiResult.success(financialReportService.getDailyReport(date));
    }

    /**
     * 获取月收入报表
     */
    @GetMapping("/monthly")
    @PreAuthorize("hasAuthority('report:view')")
    public ApiResult<FinancialReportDTO> getMonthlyReport(
            @RequestParam Integer year,
            @RequestParam Integer month) {
        return ApiResult.success(financialReportService.getMonthlyReport(year, month));
    }

    /**
     * 获取年收入报表
     */
    @GetMapping("/yearly")
    @PreAuthorize("hasAuthority('report:view')")
    public ApiResult<FinancialReportDTO> getYearlyReport(
            @RequestParam Integer year) {
        return ApiResult.success(financialReportService.getYearlyReport(year));
    }

    /**
     * 获取自定义时间段报表
     */
    @GetMapping("/custom")
    @PreAuthorize("hasAuthority('report:view')")
    public ApiResult<FinancialReportDTO> getCustomReport(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        return ApiResult.success(financialReportService.getCustomReport(startDate, endDate));
    }

    /**
     * 导出日报表
     */
    @GetMapping("/daily/export")
    @PreAuthorize("hasAuthority('report:export')")
    public void exportDailyReport(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date,
            HttpServletResponse response) throws IOException {
        financialReportService.exportDailyReport(date, response);
    }

    /**
     * 导出月报表
     */
    @GetMapping("/monthly/export")
    @PreAuthorize("hasAuthority('report:export')")
    public void exportMonthlyReport(
            @RequestParam Integer year,
            @RequestParam Integer month,
            HttpServletResponse response) throws IOException {
        financialReportService.exportMonthlyReport(year, month, response);
    }

    /**
     * 导出年报表
     */
    @GetMapping("/yearly/export")
    @PreAuthorize("hasAuthority('report:export')")
    public void exportYearlyReport(
            @RequestParam Integer year,
            HttpServletResponse response) throws IOException {
        financialReportService.exportYearlyReport(year, response);
    }

    /**
     * 导出自定义时间段报表
     */
    @GetMapping("/custom/export")
    @PreAuthorize("hasAuthority('report:export')")
    public void exportCustomReport(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            HttpServletResponse response) throws IOException {
        financialReportService.exportCustomReport(startDate, endDate, response);
    }

    /**
     * 获取收入统计（按支付方式分类）
     */
    @GetMapping("/income-by-payment-method")
    @PreAuthorize("hasAuthority('report:view')")
    public ApiResult<List<Map<String, Object>>> getIncomeByPaymentMethod(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        return ApiResult.success(financialReportService.getIncomeByPaymentMethod(startDate, endDate));
    }

    /**
     * 获取收入统计（按场馆分类）
     */
    @GetMapping("/income-by-venue")
    @PreAuthorize("hasAuthority('report:view')")
    public ApiResult<List<Map<String, Object>>> getIncomeByVenue(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        return ApiResult.success(financialReportService.getIncomeByVenue(startDate, endDate));
    }

    /**
     * 获取收入统计（按日期分组）
     */
    @GetMapping("/income-by-date")
    @PreAuthorize("hasAuthority('report:view')")
    public ApiResult<List<Map<String, Object>>> getIncomeByDate(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            @RequestParam(defaultValue = "day") String groupBy) {
        return ApiResult.success(financialReportService.getIncomeByDate(startDate, endDate, groupBy));
    }

    /**
     * 获取收入趋势
     */
    @GetMapping("/income-trend")
    @PreAuthorize("hasAuthority('report:view')")
    public ApiResult<List<Map<String, Object>>> getIncomeTrend(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        return ApiResult.success(financialReportService.getIncomeTrend(startDate, endDate));
    }

    /**
     * 获取退款统计
     */
    @GetMapping("/refund-statistics")
    @PreAuthorize("hasAuthority('report:view')")
    public ApiResult<Map<String, Object>> getRefundStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        return ApiResult.success(financialReportService.getRefundStatistics(startDate, endDate));
    }

    /**
     * 生成对账单
     */
    @PostMapping("/reconciliation")
    @PreAuthorize("hasAuthority('report:reconciliation')")
    public ApiResult<String> generateReconciliation(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {
        return ApiResult.success(financialReportService.generateReconciliation(date));
    }
}