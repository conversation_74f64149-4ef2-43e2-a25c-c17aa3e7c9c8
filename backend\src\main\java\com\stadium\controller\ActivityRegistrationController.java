package com.stadium.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stadium.common.Result;
import com.stadium.dto.ActivityRegistrationDTO;
import com.stadium.entity.ActivityRegistration;
import com.stadium.service.ActivityRegistrationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 活动报名控制器
 */
@Tag(name = "活动报名")
@RestController
@RequestMapping("/api/activity-registrations")
@RequiredArgsConstructor
public class ActivityRegistrationController {

    private final ActivityRegistrationService registrationService;

    @Operation(summary = "报名活动")
    @PostMapping
    public Result<ActivityRegistration> register(@RequestBody @Valid ActivityRegistrationDTO registrationDTO) {
        return Result.success(registrationService.register(registrationDTO));
    }

    @Operation(summary = "取消报名")
    @DeleteMapping("/{id}")
    public Result<Void> cancelRegistration(@PathVariable Long id) {
        registrationService.cancelRegistration(id);
        return Result.success();
    }

    @Operation(summary = "获取报名详情")
    @GetMapping("/{id}")
    public Result<ActivityRegistration> getRegistration(@PathVariable Long id) {
        return Result.success(registrationService.getRegistration(id));
    }

    @Operation(summary = "分页查询活动报名列表")
    @GetMapping("/activity/{activityId}")
    public Result<Page<ActivityRegistration>> listRegistrations(
            @PathVariable Long activityId,
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size) {
        return Result.success(registrationService.listRegistrations(activityId, current, size));
    }

    @Operation(summary = "分页查询用户报名列表")
    @GetMapping("/user/{userId}")
    public Result<Page<ActivityRegistration>> listUserRegistrations(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size) {
        return Result.success(registrationService.listUserRegistrations(userId, current, size));
    }

    @Operation(summary = "审核报名")
    @PostMapping("/{id}/review")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> reviewRegistration(
            @PathVariable Long id,
            @RequestParam Integer status,
            @RequestParam(required = false) String remark) {
        registrationService.reviewRegistration(id, status, remark);
        return Result.success();
    }
}