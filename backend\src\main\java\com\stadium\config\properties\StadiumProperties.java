package com.stadium.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 体育场馆系统自定义配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "stadium")
public class StadiumProperties {

    /**
     * JWT配置
     */
    private Jwt jwt = new Jwt();

    /**
     * 文件上传配置
     */
    private Upload upload = new Upload();

    /**
     * Swagger配置
     */
    private Swagger swagger = new Swagger();

    /**
     * CORS配置
     */
    private Cors cors = new Cors();

    @Data
    public static class Jwt {
        /**
         * JWT密钥
         */
        private String secret = "stadium-management-system-secret-key";

        /**
         * 访问令牌过期时间（秒）
         */
        private Long accessTokenExpiration = 3600L;

        /**
         * 刷新令牌过期时间（秒）
         */
        private Long refreshTokenExpiration = 604800L;
    }

    @Data
    public static class Upload {
        /**
         * 上传路径
         */
        private String path = "uploads/";

        /**
         * 允许的文件类型
         */
        private String allowedTypes = "jpg,jpeg,png,gif";

        /**
         * 最大文件大小
         */
        private Long maxSize = 10485760L;
    }

    @Data
    public static class Swagger {
        /**
         * 是否启用Swagger
         */
        private Boolean enabled = true;
    }

    @Data
    public static class Cors {
        /**
         * 允许的来源
         */
        private String allowedOrigins = "http://localhost:8081,http://localhost:3000";

        /**
         * 允许的方法
         */
        private String allowedMethods = "GET,POST,PUT,DELETE,OPTIONS";

        /**
         * 允许的头部
         */
        private String allowedHeaders = "*";

        /**
         * 暴露的头部
         */
        private String exposedHeaders = "Authorization,Content-Type";

        /**
         * 是否允许凭证
         */
        private Boolean allowCredentials = true;

        /**
         * 预检请求缓存时间
         */
        private Long maxAge = 3600L;
    }
}
