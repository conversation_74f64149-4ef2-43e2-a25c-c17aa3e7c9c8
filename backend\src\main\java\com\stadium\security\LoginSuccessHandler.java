package com.stadium.security;

import com.alibaba.fastjson.JSON;
import com.stadium.common.api.ApiResult;
import com.stadium.entity.LoginLog;
import com.stadium.entity.User;
import com.stadium.mapper.UserMapper;
import com.stadium.service.LoginLogService;
import com.stadium.util.IpUtil;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 登录成功处理器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LoginSuccessHandler implements AuthenticationSuccessHandler {

    private final UserMapper userMapper;
    private final LoginLogService loginLogService;

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
            Authentication authentication) throws IOException, ServletException {
        UserDetails userDetails = (UserDetails) authentication.getPrincipal();
        String username = userDetails.getUsername();
        log.info("用户 {} 登录成功", username);

        // 获取IP地址和用户代理
        String ip = IpUtil.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");

        // 记录登录日志
        LoginLog loginLog = new LoginLog();
        loginLog.setUsername(username);
        loginLog.setIp(ip);
        loginLog.setUserAgent(userAgent);
        loginLog.setStatus(1); // 成功
        loginLog.setMessage("登录成功");
        loginLog.setLoginTime(LocalDateTime.now());
        loginLogService.save(loginLog);

        // 更新用户最后登录信息
        User user = userMapper.selectByUsername(username);
        if (user != null) {
            user.setLastLoginIp(ip);
            user.setLastLoginTime(LocalDateTime.now());
            user.setStatus(1); // 1表示正常
            user.setLockTime(null);
            user.setLockExpireTime(null);
            userMapper.updateById(user);
        }

        // 返回登录成功信息
        Map<String, Object> result = new HashMap<>();

        // 添加用户信息
        if (user != null) {
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", user.getId());
            userInfo.put("username", user.getUsername());
            userInfo.put("realName", user.getRealName());
            userInfo.put("role", user.getRole());
            result.put("userInfo", userInfo);
        } else {
            log.error("登录成功但无法获取用户信息: {}", username);
            result.put("userInfo", null);
        }

        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write(JSON.toJSONString(ApiResult.success(result)));
    }
}
