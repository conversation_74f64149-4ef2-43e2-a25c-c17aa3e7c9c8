package com.stadium.config;

import com.stadium.security.LoginSuccessHandler;
import com.stadium.security.LoginFailureHandler;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import java.util.Arrays;
import java.util.List;

/**
 * Spring Security配置
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity
public class SecurityConfig {

        private final UserDetailsService userDetailsService;
        private final LoginSuccessHandler loginSuccessHandler;
        private final LoginFailureHandler loginFailureHandler;

        public SecurityConfig(
                        @Qualifier("customUserDetailsService") UserDetailsService userDetailsService,
                        LoginSuccessHandler loginSuccessHandler,
                        LoginFailureHandler loginFailureHandler) {
                this.userDetailsService = userDetailsService;
                this.loginSuccessHandler = loginSuccessHandler;
                this.loginFailureHandler = loginFailureHandler;
        }

        @Bean
        public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
                http
                                // 配置CORS
                                .cors(cors -> cors.configurationSource(corsConfigurationSource()))
                                // 禁用CSRF
                                .csrf(csrf -> csrf.disable())
                                // 配置会话管理
                                .sessionManagement(session -> session
                                                .sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED))
                                // 配置请求授权
                                .authorizeHttpRequests(auth -> {
                                        auth
                                                        .requestMatchers("/api/auth/**").permitAll()
                                                        .requestMatchers("/api/users/login").permitAll()
                                                        .requestMatchers("/api/users/register").permitAll()
                                                        .requestMatchers("/api/captcha/**").permitAll()
                                                        .requestMatchers("/api/venues").permitAll()
                                                        .requestMatchers("/api/venues/**").permitAll()
                                                        .requestMatchers("/api/activities").permitAll()
                                                        .requestMatchers("/api/activities/**").permitAll()
                                                        .requestMatchers("/api/member/levels").permitAll()
                                                        .requestMatchers("/api/member/**").permitAll()
                                                        .requestMatchers("/swagger-ui/**", "/swagger-ui.html")
                                                        .permitAll()
                                                        .requestMatchers("/swagger-resources/**").permitAll()
                                                        .requestMatchers("/v3/api-docs/**", "/v2/api-docs/**")
                                                        .permitAll()
                                                        .requestMatchers("/webjars/**").permitAll()
                                                        // 允许OPTIONS请求
                                                        .requestMatchers(org.springframework.http.HttpMethod.OPTIONS,
                                                                        "/**")
                                                        .permitAll()
                                                        // 其他请求需要认证
                                                        .anyRequest().authenticated();
                                })
                                // 配置表单登录
                                .formLogin(form -> {
                                        form
                                                        .loginProcessingUrl("/api/users/login")
                                                        .successHandler(loginSuccessHandler)
                                                        .failureHandler(loginFailureHandler)
                                                        .permitAll();
                                })
                                // 配置登出
                                .logout(logout -> {
                                        logout
                                                        .logoutUrl("/api/users/logout")
                                                        .logoutSuccessUrl("/api/users/login")
                                                        .invalidateHttpSession(true)
                                                        .deleteCookies("JSESSIONID")
                                                        .permitAll();
                                })
                                .authenticationProvider(authenticationProvider());

                return http.build();
        }

        @Bean
        public CorsConfigurationSource corsConfigurationSource() {
                CorsConfiguration configuration = new CorsConfiguration();
                configuration.setAllowedOrigins(
                                List.of("http://localhost:8080", "http://localhost:8082", "http://localhost:8083"));
                configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
                configuration.setAllowedHeaders(Arrays.asList("Authorization", "Content-Type", "X-Requested-With",
                                "Accept",
                                "Origin", "Access-Control-Request-Method", "Access-Control-Request-Headers"));
                configuration.setExposedHeaders(Arrays.asList("Authorization", "Captcha-Id"));
                configuration.setAllowCredentials(true);
                configuration.setMaxAge(3600L);

                UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
                source.registerCorsConfiguration("/**", configuration);
                return source;
        }

        @Bean
        public AuthenticationProvider authenticationProvider() {
                DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
                authProvider.setUserDetailsService(userDetailsService);
                authProvider.setPasswordEncoder(passwordEncoder());
                return authProvider;
        }

        @Bean
        public PasswordEncoder passwordEncoder() {
                return new BCryptPasswordEncoder();
        }
}