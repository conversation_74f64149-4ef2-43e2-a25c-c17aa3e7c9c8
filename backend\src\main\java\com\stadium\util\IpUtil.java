package com.stadium.util;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * IP工具类
 */
@Slf4j
public class IpUtil {
    private static final String UNKNOWN = "unknown";
    private static final String LOCALHOST_IPV4 = "127.0.0.1";
    private static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";
    private static final String SEPARATOR = ",";

    /**
     * 获取客户端IP地址
     *
     * @param request 请求对象
     * @return IP地址
     */
    public static String getIpAddr(HttpServletRequest request) {
        String ipAddress = null;
        try {
            // 获取X-Forwarded-For头部
            ipAddress = request.getHeader("X-Forwarded-For");

            // 如果通过代理，则获取第一个IP地址
            if (ipAddress != null && !ipAddress.isEmpty() && !UNKNOWN.equalsIgnoreCase(ipAddress)) {
                // 多次反向代理后会有多个IP值，第一个为真实IP
                int index = ipAddress.indexOf(SEPARATOR);
                if (index != -1) {
                    ipAddress = ipAddress.substring(0, index);
                }
            }

            // 如果没有X-Forwarded-For头部，则尝试其他头部
            if (StringUtils.isEmpty(ipAddress) || UNKNOWN.equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader("Proxy-Client-IP");
            }

            if (StringUtils.isEmpty(ipAddress) || UNKNOWN.equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader("WL-Proxy-Client-IP");
            }

            if (StringUtils.isEmpty(ipAddress) || UNKNOWN.equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader("HTTP_CLIENT_IP");
            }

            if (StringUtils.isEmpty(ipAddress) || UNKNOWN.equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader("HTTP_X_FORWARDED_FOR");
            }

            // 如果仍然没有获取到IP，则使用getRemoteAddr
            if (StringUtils.isEmpty(ipAddress) || UNKNOWN.equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getRemoteAddr();

                // 如果是本地访问，则根据网卡获取本机IP
                if (LOCALHOST_IPV4.equals(ipAddress) || LOCALHOST_IPV6.equals(ipAddress)) {
                    try {
                        InetAddress inet = InetAddress.getLocalHost();
                        ipAddress = inet.getHostAddress();
                    } catch (UnknownHostException e) {
                        log.error("获取本机IP失败", e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取客户端IP地址失败", e);
            ipAddress = "unknown";
        }

        return ipAddress;
    }

    /**
     * 兼容旧方法
     */
    public static String getIpAddress(HttpServletRequest request) {
        return getIpAddr(request);
    }

    /**
     * 检查IP地址是否合法
     *
     * @param ip IP地址
     * @return 是否合法
     */
    public static boolean isValidIp(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }

        // 简单的IPv4地址验证
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }

        for (String part : parts) {
            try {
                int value = Integer.parseInt(part);
                if (value < 0 || value > 255) {
                    return false;
                }
            } catch (NumberFormatException e) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取IP地址的整数表示
     *
     * @param ip IP地址
     * @return 整数表示
     */
    public static long ipToLong(String ip) {
        String[] parts = ip.split("\\.");
        long result = 0;

        for (int i = 0; i < parts.length; i++) {
            int part = Integer.parseInt(parts[i]);
            result = result * 256 + part;
        }

        return result;
    }

    /**
     * 将整数转换为IP地址
     *
     * @param ip 整数表示的IP地址
     * @return IP地址字符串
     */
    public static String longToIp(long ip) {
        StringBuilder sb = new StringBuilder();

        sb.append((ip >> 24) & 0xFF).append(".");
        sb.append((ip >> 16) & 0xFF).append(".");
        sb.append((ip >> 8) & 0xFF).append(".");
        sb.append(ip & 0xFF);

        return sb.toString();
    }
}