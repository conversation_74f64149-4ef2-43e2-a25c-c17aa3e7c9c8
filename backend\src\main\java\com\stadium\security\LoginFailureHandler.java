package com.stadium.security;

import com.alibaba.fastjson.JSON;
import com.stadium.common.api.ApiResult;
import com.stadium.entity.LoginLog;
import com.stadium.service.LoginLogService;
import com.stadium.util.IpUtil;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;

/**
 * 登录失败处理器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LoginFailureHandler implements AuthenticationFailureHandler {

    private final LoginLogService loginLogService;

    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
            AuthenticationException exception) throws IOException, ServletException {
        log.error("登录失败: {}", exception.getMessage());

        // 获取用户名和IP地址
        String username = request.getParameter("username");
        String ip = IpUtil.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");

        // 记录登录日志
        LoginLog loginLog = new LoginLog();
        loginLog.setUsername(username);
        loginLog.setIp(ip);
        loginLog.setUserAgent(userAgent);
        loginLog.setStatus(0); // 失败
        loginLog.setMessage(exception.getMessage());
        loginLog.setLoginTime(LocalDateTime.now());
        loginLogService.save(loginLog);

        // 处理不同类型的认证异常
        String errorMessage;
        if (exception instanceof BadCredentialsException) {
            errorMessage = "用户名或密码错误";
        } else if (exception instanceof DisabledException) {
            errorMessage = "账号已被禁用";
        } else if (exception instanceof LockedException) {
            errorMessage = "账号已被锁定";
        } else {
            errorMessage = "登录失败: " + exception.getMessage();
        }

        // 返回登录失败信息
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.getWriter().write(JSON.toJSONString(ApiResult.failed(errorMessage)));
    }
}
