package com.stadium.controller;

import com.stadium.common.api.ApiResult;
import com.stadium.entity.SystemMessage;
import com.stadium.entity.UserMessage;
import com.stadium.service.SystemMessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 消息管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/messages")
@Tag(name = "消息管理接口")
@RequiredArgsConstructor
public class MessageController {

    private final SystemMessageService systemMessageService;

    /**
     * 发送系统通知
     */
    @PostMapping("/system/send")
    @Operation(summary = "发送系统通知")
    @PreAuthorize("hasAuthority('message:send')")
    public ApiResult<Boolean> sendSystemNotification(@RequestParam String title,
            @RequestParam String content,
            @RequestParam Integer receiverType,
            @RequestParam(required = false) String receiverIds,
            @RequestParam Long senderId) {
        boolean result = systemMessageService.sendSystemNotification(title, content, receiverType, receiverIds,
                senderId);
        return ApiResult.success(result);
    }

    /**
     * 发送预订提醒
     */
    @PostMapping("/booking/reminder")
    @Operation(summary = "发送预订提醒")
    public ApiResult<Boolean> sendBookingReminder(@RequestParam Long userId,
            @RequestParam String title,
            @RequestParam String content,
            @RequestParam(required = false) String businessId) {
        boolean result = systemMessageService.sendBookingReminder(userId, title, content, businessId);
        return ApiResult.success(result);
    }

    /**
     * 发送活动通知
     */
    @PostMapping("/activity/notification")
    @Operation(summary = "发送活动通知")
    @PreAuthorize("hasAuthority('message:send')")
    public ApiResult<Boolean> sendActivityNotification(@RequestParam String title,
            @RequestParam String content,
            @RequestParam Integer receiverType,
            @RequestParam(required = false) String receiverIds,
            @RequestParam(required = false) String businessId) {
        boolean result = systemMessageService.sendActivityNotification(title, content, receiverType, receiverIds,
                businessId);
        return ApiResult.success(result);
    }

    /**
     * 发送支付通知
     */
    @PostMapping("/payment/notification")
    @Operation(summary = "发送支付通知")
    public ApiResult<Boolean> sendPaymentNotification(@RequestParam Long userId,
            @RequestParam String title,
            @RequestParam String content,
            @RequestParam(required = false) String orderNo) {
        boolean result = systemMessageService.sendPaymentNotification(userId, title, content, orderNo);
        return ApiResult.success(result);
    }

    /**
     * 发送维护通知
     */
    @PostMapping("/maintenance/notification")
    @Operation(summary = "发送维护通知")
    @PreAuthorize("hasAuthority('message:send')")
    public ApiResult<Boolean> sendMaintenanceNotification(@RequestParam String title,
            @RequestParam String content) {
        boolean result = systemMessageService.sendMaintenanceNotification(title, content);
        return ApiResult.success(result);
    }

    /**
     * 获取用户未读消息数量
     */
    @GetMapping("/unread-count")
    @Operation(summary = "获取未读消息数量")
    public ApiResult<Integer> getUnreadCount(@RequestParam Long userId) {
        int count = systemMessageService.getUnreadCount(userId);
        return ApiResult.success(count);
    }

    /**
     * 获取用户消息列表
     */
    @GetMapping("/user")
    @Operation(summary = "获取用户消息列表")
    public ApiResult<List<UserMessage>> getUserMessages(@RequestParam Long userId,
            @RequestParam(required = false) Integer type,
            @RequestParam(required = false) Boolean isRead,
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        List<UserMessage> messages = systemMessageService.getUserMessages(userId, type, isRead, pageNum, pageSize);
        return ApiResult.success(messages);
    }

    /**
     * 标记消息为已读
     */
    @PostMapping("/mark-read")
    @Operation(summary = "标记消息为已读")
    public ApiResult<Boolean> markAsRead(@RequestParam Long userId,
            @RequestParam Long messageId) {
        boolean result = systemMessageService.markAsRead(userId, messageId);
        return ApiResult.success(result);
    }

    /**
     * 批量标记消息为已读
     */
    @PostMapping("/mark-all-read")
    @Operation(summary = "批量标记消息为已读")
    public ApiResult<Boolean> markAllAsRead(@RequestParam Long userId,
            @RequestParam(required = false) Integer type) {
        boolean result = systemMessageService.markAllAsRead(userId, type);
        return ApiResult.success(result);
    }

    /**
     * 删除用户消息
     */
    @DeleteMapping("/user")
    @Operation(summary = "删除用户消息")
    public ApiResult<Boolean> deleteUserMessage(@RequestParam Long userId,
            @RequestParam Long messageId) {
        boolean result = systemMessageService.deleteUserMessage(userId, messageId);
        return ApiResult.success(result);
    }

    /**
     * 收藏/取消收藏消息
     */
    @PostMapping("/toggle-favorite")
    @Operation(summary = "收藏/取消收藏消息")
    public ApiResult<Boolean> toggleFavorite(@RequestParam Long userId,
            @RequestParam Long messageId) {
        boolean result = systemMessageService.toggleFavorite(userId, messageId);
        return ApiResult.success(result);
    }

    /**
     * 获取消息统计
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取消息统计")
    public ApiResult<Map<String, Object>> getMessageStatistics(@RequestParam Long userId) {
        Map<String, Object> statistics = systemMessageService.getMessageStatistics(userId);
        return ApiResult.success(statistics);
    }

    /**
     * 获取系统消息列表（管理员）
     */
    @GetMapping("/system")
    @Operation(summary = "获取系统消息列表")
    @PreAuthorize("hasAuthority('message:manage')")
    public ApiResult<List<SystemMessage>> getSystemMessages(@RequestParam(required = false) Integer type,
            @RequestParam(required = false) Integer status,
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        List<SystemMessage> messages = systemMessageService.getSystemMessages(type, status, pageNum, pageSize);
        return ApiResult.success(messages);
    }

    /**
     * 撤回消息
     */
    @PostMapping("/recall")
    @Operation(summary = "撤回消息")
    @PreAuthorize("hasAuthority('message:recall')")
    public ApiResult<Boolean> recallMessage(@RequestParam Long messageId,
            @RequestParam Long operatorId) {
        boolean result = systemMessageService.recallMessage(messageId, operatorId);
        return ApiResult.success(result);
    }

    /**
     * 定时发送消息
     */
    @PostMapping("/schedule")
    @Operation(summary = "定时发送消息")
    @PreAuthorize("hasAuthority('message:schedule')")
    public ApiResult<Boolean> scheduleMessage(@RequestBody SystemMessage message,
            @RequestParam LocalDateTime scheduledTime) {
        boolean result = systemMessageService.scheduleMessage(message, scheduledTime);
        return ApiResult.success(result);
    }

    /**
     * 获取消息发送统计（管理员）
     */
    @GetMapping("/send-statistics")
    @Operation(summary = "获取消息发送统计")
    @PreAuthorize("hasAuthority('message:statistics')")
    public ApiResult<Map<String, Object>> getSendStatistics(@RequestParam LocalDateTime startTime,
            @RequestParam LocalDateTime endTime) {
        Map<String, Object> statistics = systemMessageService.getSendStatistics(startTime, endTime);
        return ApiResult.success(statistics);
    }

    /**
     * 清理过期消息
     */
    @PostMapping("/clean-expired")
    @Operation(summary = "清理过期消息")
    @PreAuthorize("hasAuthority('message:clean')")
    public ApiResult<String> cleanExpiredMessages() {
        systemMessageService.cleanExpiredMessages();
        return ApiResult.success("过期消息清理完成");
    }
}
