package com.stadium.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "会员卡信息")
public class MemberCardDTO {

    @Schema(description = "会员卡ID")
    private Long id;

    @Schema(description = "用户ID")
    @NotNull(message = "会员ID不能为空")
    private Long memberId;

    @Schema(description = "会员卡号")
    @NotBlank(message = "会员卡号不能为空")
    private String cardNo;

    @NotNull(message = "会员卡类型不能为空")
    @Schema(description = "会员卡类型：1-普通卡，2-金卡，3-钻石卡", required = true)
    private Integer type;

    @Schema(description = "余额")
    private BigDecimal balance;

    @Schema(description = "积分")
    private Integer points;

    @Schema(description = "会员卡状态：0-未激活，1-已激活，2-已过期")
    private Integer status;

    @Schema(description = "激活时间")
    private LocalDateTime activateTime;

    @Schema(description = "过期时间")
    private LocalDateTime expireTime;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @NotBlank(message = "会员卡名称不能为空")
    @Schema(description = "会员卡名称", required = true)
    private String name;

    @NotNull(message = "会员卡价格不能为空")
    @Schema(description = "会员卡价格", required = true)
    private BigDecimal price;

    @Schema(description = "会员卡描述")
    private String description;
}