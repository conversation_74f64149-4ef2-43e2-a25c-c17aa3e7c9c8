package com.stadium.config;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.annotation.Configuration;

/**
 * Bean后处理器配置
 * 用于解决ddlApplicationRunner Bean冲突问题
 */
@Configuration
public class BeanPostProcessorConfig implements BeanFactoryPostProcessor {

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        // 检查是否存在ddlApplicationRunner Bean
        if (beanFactory.containsBean("ddlApplicationRunner")) {
            try {
                // 移除有问题的Bean定义
                if (beanFactory instanceof DefaultListableBeanFactory) {
                    ((DefaultListableBeanFactory) beanFactory).removeBeanDefinition("ddlApplicationRunner");
                    System.out.println("已移除有问题的ddlApplicationRunner Bean");
                }
            } catch (Exception e) {
                System.out.println("移除ddlApplicationRunner Bean失败: " + e.getMessage());
            }
        }
    }
}
