# 体育场馆管理系统内部化指南

## 📋 系统内部化概述

体育场馆管理系统已完全实现内部化，所有功能都在系统内部完成，不依赖任何外部第三方服务。系统可以在完全离线环境下正常运行。

## 🏗️ 内部化架构特性

### 1. 站内支付系统 💰
- **完全自主**: 不依赖微信支付、支付宝等外部支付接口
- **余额管理**: 用户钱包系统，支持充值、消费、退款
- **交易记录**: 完整的交易流水和统计功能
- **安全保障**: 支付密码、乐观锁防并发

**核心功能**:
- 用户钱包管理 (`UserWallet`)
- 交易记录追踪 (`WalletTransaction`)
- 余额充值/消费/退款
- 金额冻结/解冻机制
- 交易统计和报表

### 2. 本地文件存储 📁
- **本地存储**: 所有文件存储在本地文件系统
- **多类型支持**: 图片、文档、Excel、PDF等
- **目录管理**: 自动创建和管理上传目录
- **访问控制**: 通过Spring MVC配置文件访问

**存储目录结构**:
```
uploads/
├── images/          # 场馆图片
├── avatars/         # 用户头像
├── documents/       # 文档文件
├── exports/         # 导出文件
└── reconciliation/  # 对账单文件
```

### 3. 站内消息系统 📨
- **完全内部**: 不依赖短信、邮件等外部服务
- **实时通知**: 系统内消息推送和提醒
- **多类型消息**: 系统通知、预订提醒、活动通知等
- **消息管理**: 已读/未读、收藏、删除等功能

**消息类型**:
- 系统通知 (type=1)
- 预订提醒 (type=2)
- 活动通知 (type=3)
- 支付通知 (type=4)
- 维护通知 (type=5)

### 4. 数据统计和报表 📊
- **本地数据**: 所有统计基于本地数据库数据
- **Excel导出**: 支持各类报表的Excel导出
- **对账单生成**: 自动生成财务对账单
- **实时统计**: 实时计算各类业务数据

## 🔧 技术实现细节

### 支付系统实现
```java
// 钱包服务核心方法
@Service
public class UserWalletServiceImpl {
    // 充值
    public boolean recharge(Long userId, BigDecimal amount, String description, Long operatorId)
    
    // 消费
    public boolean consume(Long userId, BigDecimal amount, String description, String orderNo)
    
    // 退款
    public boolean refund(Long userId, BigDecimal amount, String description, String orderNo)
    
    // 余额检查
    public boolean checkBalance(Long userId, BigDecimal amount)
}
```

### 文件存储实现
```java
// 文件上传配置
@Configuration
public class FileUploadConfig {
    @PostConstruct
    public void init() {
        // 自动创建所需目录
        createDirectoryIfNotExists(path + "images/");
        createDirectoryIfNotExists(path + "avatars/");
        createDirectoryIfNotExists(path + "documents/");
        createDirectoryIfNotExists(path + "exports/");
        createDirectoryIfNotExists(path + "reconciliation/");
    }
}
```

### 消息系统实现
```java
// 消息服务核心方法
@Service
public class SystemMessageServiceImpl {
    // 发送系统通知
    public boolean sendSystemNotification(String title, String content, Integer receiverType, String receiverIds, Long senderId)
    
    // 发送预订提醒
    public boolean sendBookingReminder(Long userId, String title, String content, String businessId)
    
    // 发送支付通知
    public boolean sendPaymentNotification(Long userId, String title, String content, String orderNo)
}
```

## 🚀 部署和配置

### 1. 数据库配置
```sql
-- 钱包系统表
CREATE TABLE user_wallet (...);
CREATE TABLE wallet_transaction (...);

-- 消息系统表
CREATE TABLE system_message (...);
CREATE TABLE user_message (...);
```

### 2. 应用配置
```yaml
# application.yml
upload:
  path: uploads/
  allowedTypes: jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx
  maxSize: 10MB

sms:
  enabled: false  # 禁用外部短信服务
  mock: true      # 仅支持站内消息

stadium:
  upload:
    path: uploads/
    allowedTypes: jpg,jpeg,png,gif
    maxSize: 10485760
```

### 3. 目录权限
```bash
# 确保上传目录有正确的读写权限
chmod 755 uploads/
chmod 755 uploads/images/
chmod 755 uploads/avatars/
chmod 755 uploads/documents/
chmod 755 uploads/exports/
chmod 755 uploads/reconciliation/
```

## ✅ 内部化验证清单

### 支付系统验证
- [ ] 用户钱包创建和管理
- [ ] 余额充值功能
- [ ] 订单支付功能
- [ ] 退款处理功能
- [ ] 交易记录查询
- [ ] 财务统计报表

### 文件系统验证
- [ ] 图片上传和访问
- [ ] 文档上传和下载
- [ ] Excel报表导出
- [ ] 对账单生成
- [ ] 文件删除功能

### 消息系统验证
- [ ] 系统通知发送
- [ ] 预订提醒功能
- [ ] 支付通知推送
- [ ] 消息已读/未读状态
- [ ] 消息收藏和删除

### 离线运行验证
- [ ] 断网环境下系统正常启动
- [ ] 所有核心功能正常使用
- [ ] 数据统计和报表生成
- [ ] 用户操作流程完整

## 🔒 安全特性

### 1. 支付安全
- 支付密码加密存储
- 乐观锁防止并发问题
- 交易记录完整审计
- 余额变动实时监控

### 2. 文件安全
- 文件类型白名单验证
- 文件大小限制
- 路径遍历攻击防护
- 访问权限控制

### 3. 消息安全
- 消息内容XSS防护
- 用户权限验证
- 消息过期自动清理
- 敏感信息脱敏

## 📈 性能优化

### 1. 数据库优化
- 合理的索引设计
- 分页查询优化
- 连接池配置
- 查询缓存启用

### 2. 文件处理优化
- 文件上传大小限制
- 异步文件处理
- 静态资源缓存
- 文件压缩优化

### 3. 消息处理优化
- 批量消息发送
- 消息队列处理
- 过期消息清理
- 消息统计缓存

## 🎯 使用建议

1. **定期备份**: 定期备份数据库和上传文件
2. **监控日志**: 关注系统日志，及时发现问题
3. **性能监控**: 监控系统性能指标
4. **安全更新**: 定期更新系统安全配置
5. **容量规划**: 根据使用情况规划存储容量

## 📞 技术支持

如有任何技术问题，请参考：
- 系统日志文件
- 数据库错误日志
- 应用配置文件
- 本文档的故障排除部分

---

**注意**: 本系统已完全实现内部化，可在完全离线环境下运行，无需任何外部服务依赖。
