package com.stadium.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stadium.common.api.ApiResult;
import com.stadium.dto.VenueFacilityDTO;
import com.stadium.entity.VenueFacility;
import com.stadium.service.VenueFacilityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 场馆设施管理控制器
 */
@Tag(name = "场馆设施管理")
@RestController
@RequestMapping("/api/venue-facilities")
@RequiredArgsConstructor
public class VenueFacilityController {

    private final VenueFacilityService venueFacilityService;

    @Operation(summary = "创建场馆设施")
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResult<VenueFacility> createVenueFacility(@RequestBody @Valid VenueFacilityDTO venueFacilityDTO) {
        return ApiResult.success(venueFacilityService.createVenueFacility(venueFacilityDTO));
    }

    @Operation(summary = "更新场馆设施")
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResult<VenueFacility> updateVenueFacility(@PathVariable Long id,
            @RequestBody @Valid VenueFacilityDTO venueFacilityDTO) {
        return ApiResult.success(venueFacilityService.updateVenueFacility(id, venueFacilityDTO));
    }

    @Operation(summary = "删除场馆设施")
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResult<Void> deleteVenueFacility(@PathVariable Long id) {
        venueFacilityService.deleteVenueFacility(id);
        return ApiResult.success();
    }

    @Operation(summary = "获取场馆设施详情")
    @GetMapping("/{id}")
    public ApiResult<VenueFacility> getVenueFacility(@PathVariable Long id) {
        return ApiResult.success(venueFacilityService.getVenueFacility(id));
    }

    @Operation(summary = "分页查询场馆设施列表")
    @GetMapping
    public ApiResult<Page<VenueFacility>> listVenueFacilities(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword) {
        return ApiResult.success(venueFacilityService.listVenueFacilities(current, size, keyword));
    }
}