package com.stadium.validation.annotation;

import com.stadium.validation.validator.NoSqlInjectionValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * 禁止SQL注入攻击注解
 */
@Documented
@Constraint(validatedBy = NoSqlInjectionValidator.class)
@Target({ ElementType.FIELD, ElementType.PARAMETER })
@Retention(RetentionPolicy.RUNTIME)
public @interface NoSqlInjection {

    String message() default "包含SQL注入攻击特征";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
