server:
  port: 0  # 随机端口

spring:
  application:
    name: stadium-management-system-test
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
  h2:
    console:
      enabled: true
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    root: warn
    '[com.stadium]': info

# 自定义配置
stadium:
  jwt:
    secret: test-secret-key
    access-token-expiration: 3600
    refresh-token-expiration: 604800
  upload:
    path: test-uploads/
    allowed-types: jpg,jpeg,png,gif
    max-size: 10485760
  swagger:
    enabled: false  # 测试时禁用Swagger
  cors:
    allowed-origins: http://localhost:8081
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "*"
    exposed-headers: Authorization,Content-Type
    allow-credentials: true
    max-age: 3600

# 性能监控配置
performance:
  monitor:
    enabled: false  # 测试时禁用性能监控

# 缓存配置
cache-config:
  local-enabled: true
  redis-enabled: false  # 测试时禁用Redis
