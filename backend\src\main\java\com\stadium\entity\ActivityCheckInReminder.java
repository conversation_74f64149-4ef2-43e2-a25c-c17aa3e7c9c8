package com.stadium.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 活动签到提醒记录
 */
@Data
@TableName("activity_check_in_reminder")
@Schema(description = "活动签到提醒记录")
public class ActivityCheckInReminder {

    @TableId(type = IdType.AUTO)
    @Schema(description = "ID")
    private Long id;

    @Schema(description = "活动ID")
    @TableField("activity_id")
    private Long activityId;

    @Schema(description = "用户ID")
    @TableField("user_id")
    private Long userId;

    @Schema(description = "提醒类型：1-活动开始前提醒，2-未签到提醒")
    @TableField("reminder_type")
    private Integer reminderType;

    @Schema(description = "提醒内容")
    @TableField("content")
    private String content;

    @Schema(description = "提醒方式：1-邮件，2-短信，3-站内信")
    @TableField("reminder_method")
    private Integer reminderMethod;

    @Schema(description = "状态：0-未发送，1-已发送，2-发送失败")
    @TableField("status")
    private Integer status;

    @Schema(description = "发送时间")
    @TableField("send_time")
    private LocalDateTime sendTime;

    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;
}